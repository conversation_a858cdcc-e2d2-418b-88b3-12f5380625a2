-- 清空数据库表的SQL脚本
-- 数据库：docvault 和 nebula
-- 注意：此脚本将删除所有表中的数据，请谨慎使用
-- 建议在执行前备份数据库

-- 禁用外键约束检查（MySQL）
SET FOREIGN_KEY_CHECKS = 0;

-- 清空书籍相关表
TRUNCATE TABLE `docvault`.`book_users_borrow`;
TRUNCATE TABLE `docvault`.`book_users_receive`;
TRUNCATE TABLE `docvault`.`books`;

-- 清空借阅相关表
TRUNCATE TABLE `docvault`.`borrow_document_relations`;
TRUNCATE TABLE `docvault`.`borrow_records`;

-- 清空发放相关表
TRUNCATE TABLE `docvault`.`distribute_record_files`;
TRUNCATE TABLE `docvault`.`distribute_record_permissions`;
TRUNCATE TABLE `docvault`.`distribute_records`;

-- 清空处置相关表
TRUNCATE TABLE `docvault`.`disposal_record_files`;
TRUNCATE TABLE `docvault`.`disposal_record_permissions`;
TRUNCATE TABLE `docvault`.`disposal_records`;

-- 清空回收相关表
TRUNCATE TABLE `docvault`.`recycle_record_files`;
TRUNCATE TABLE `docvault`.`recycle_record_permissions`;
TRUNCATE TABLE `docvault`.`recycle_records`;

-- 清空文档库表
TRUNCATE TABLE `docvault`.`external_document_library`;
TRUNCATE TABLE `docvault`.`internal_document_library`;

-- 清空nebula数据库相关表
TRUNCATE TABLE `nebula`.`business_dictionary_node_relation`;

-- 重新启用外键约束检查（MySQL）
SET FOREIGN_KEY_CHECKS = 1;

-- 执行完成提示
SELECT '所有指定表已清空完成' AS result;
