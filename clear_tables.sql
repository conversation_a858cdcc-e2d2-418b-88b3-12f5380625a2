-- 清空数据库表的SQL脚本
-- 数据库：docvault 和 nebula
-- 注意：此脚本将删除所有表中的数据，请谨慎使用
-- 建议在执行前备份数据库

-- 禁用外键约束检查（MySQL）
SET FOREIGN_KEY_CHECKS = 0;

-- 清空书籍相关表
TRUNCATE TABLE `docvault`.`book_users_borrow`;
TRUNCATE TABLE `docvault`.`book_users_receive`;
TRUNCATE TABLE `docvault`.`books`;

-- 清空借阅相关表
TRUNCATE TABLE `docvault`.`borrow_document_relations`;
TRUNCATE TABLE `docvault`.`borrow_records`;

-- 清空发放相关表
TRUNCATE TABLE `docvault`.`distribute_record_files`;
TRUNCATE TABLE `docvault`.`distribute_record_permissions`;
TRUNCATE TABLE `docvault`.`distribute_records`;

-- 清空处置相关表
TRUNCATE TABLE `docvault`.`disposal_record_files`;
TRUNCATE TABLE `docvault`.`disposal_record_permissions`;
TRUNCATE TABLE `docvault`.`disposal_records`;

-- 清空回收相关表
TRUNCATE TABLE `docvault`.`recycle_record_files`;
TRUNCATE TABLE `docvault`.`recycle_record_permissions`;
TRUNCATE TABLE `docvault`.`recycle_records`;

-- 清空文档库表
TRUNCATE TABLE `docvault`.`external_document_library`;
TRUNCATE TABLE `docvault`.`internal_document_library`;

-- 清空nebula数据库相关表
TRUNCATE TABLE `nebula`.`business_dictionary_node_relation`;

-- 清空phoenix数据库相关表
-- 应用版本相关表
TRUNCATE TABLE `phoenix`.`app_newest_version`;
TRUNCATE TABLE `phoenix`.`app_newest_version_tenant`;
TRUNCATE TABLE `phoenix`.`app_version`;
TRUNCATE TABLE `phoenix`.`app_version_tenant`;
TRUNCATE TABLE `phoenix`.`app_version_update_record`;

-- 业务日志相关表
TRUNCATE TABLE `phoenix`.`business_operation_log`;
TRUNCATE TABLE `phoenix`.`conversion_record`;
TRUNCATE TABLE `phoenix`.`data_export_record`;
TRUNCATE TABLE `phoenix`.`login_logs`;
TRUNCATE TABLE `phoenix`.`operation_logs`;
TRUNCATE TABLE `phoenix`.`system_login_log_info`;
TRUNCATE TABLE `phoenix`.`system_operation_log_info`;

-- 权限相关表
TRUNCATE TABLE `phoenix`.`casbin_rules`;
TRUNCATE TABLE `phoenix`.`role_apis`;
TRUNCATE TABLE `phoenix`.`role_buttons`;
TRUNCATE TABLE `phoenix`.`role_groups`;
TRUNCATE TABLE `phoenix`.`role_menus`;
TRUNCATE TABLE `phoenix`.`role_users`;

-- 字典相关表
TRUNCATE TABLE `phoenix`.`dictionary`;
TRUNCATE TABLE `phoenix`.`ent_types`;

-- 文件相关表
TRUNCATE TABLE `phoenix`.`fms_file`;
TRUNCATE TABLE `phoenix`.`more_help_file`;

-- 节点相关表
TRUNCATE TABLE `phoenix`.`node`;
TRUNCATE TABLE `phoenix`.`node_reviewer_task`;
TRUNCATE TABLE `phoenix`.`node_template`;
TRUNCATE TABLE `phoenix`.`node_template_reviewer`;

-- 组织相关表
TRUNCATE TABLE `phoenix`.`organization_closure`;
TRUNCATE TABLE `phoenix`.`user_belong_company`;
TRUNCATE TABLE `phoenix`.`user_groups`;
TRUNCATE TABLE `phoenix`.`user_organizations`;
TRUNCATE TABLE `phoenix`.`user_positions_organizations`;

-- 插件相关表
TRUNCATE TABLE `phoenix`.`plugin`;
TRUNCATE TABLE `phoenix`.`plugin_button_rel`;
TRUNCATE TABLE `phoenix`.`plugin_menu_rel`;
TRUNCATE TABLE `phoenix`.`tenant_plugin`;

-- SaaS相关表
TRUNCATE TABLE `phoenix`.`saas_api`;
TRUNCATE TABLE `phoenix`.`saas_application`;
TRUNCATE TABLE `phoenix`.`saas_button`;
TRUNCATE TABLE `phoenix`.`saas_group`;
TRUNCATE TABLE `phoenix`.`saas_group_type`;
TRUNCATE TABLE `phoenix`.`saas_menu`;
TRUNCATE TABLE `phoenix`.`saas_organization`;
TRUNCATE TABLE `phoenix`.`saas_organization_user_info`;
TRUNCATE TABLE `phoenix`.`saas_position`;
TRUNCATE TABLE `phoenix`.`saas_role`;
TRUNCATE TABLE `phoenix`.`saas_tenant`;
TRUNCATE TABLE `phoenix`.`saas_tenant_user_info`;
TRUNCATE TABLE `phoenix`.`saas_token`;
TRUNCATE TABLE `phoenix`.`saas_user`;

-- 租户用户相关表
TRUNCATE TABLE `phoenix`.`tenant_users`;

-- 工作流相关表
TRUNCATE TABLE `phoenix`.`workflow`;
TRUNCATE TABLE `phoenix`.`workflow_cc`;
TRUNCATE TABLE `phoenix`.`workflow_start_callback_config`;
TRUNCATE TABLE `phoenix`.`workflow_template`;
TRUNCATE TABLE `phoenix`.`workflow_template_cc_review`;
TRUNCATE TABLE `phoenix`.`workflow_template_department`;
TRUNCATE TABLE `phoenix`.`workflow_template_main`;
TRUNCATE TABLE `phoenix`.`workflow_template_node`;
TRUNCATE TABLE `phoenix`.`workflow_template_node_review`;
TRUNCATE TABLE `phoenix`.`workflow_template_preset`;
TRUNCATE TABLE `phoenix`.`workflow_template_version`;

-- 视图表（注意：视图不能使用TRUNCATE，使用DELETE）
DELETE FROM `phoenix`.`view_business_operation_log`;
DELETE FROM `phoenix`.`view_online_user`;
DELETE FROM `phoenix`.`view_organization_user_count`;
DELETE FROM `phoenix`.`view_organization_user_id`;
DELETE FROM `phoenix`.`view_tenant_plugin`;
DELETE FROM `phoenix`.`view_tenant_plugin_menu`;
DELETE FROM `phoenix`.`view_user_recent_login_log`;

-- 重新启用外键约束检查（MySQL）
SET FOREIGN_KEY_CHECKS = 1;

-- 执行完成提示
SELECT '所有指定表已清空完成' AS result;
