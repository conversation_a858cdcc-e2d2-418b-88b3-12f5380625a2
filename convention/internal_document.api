syntax = "v1"


type CreateInternalDocumentReq {
    Name string `json:"name"`
    EnglishName string `json:"englishName,optional"` // 0.5版本新增：文件英文名称
    FileID string `json:"fileId,optional"`
    DocCategoryID string `json:"docCategoryId"`
    DepartmentIDs []string `json:"departmentIds"` // 0.4版本调整：编制部门改为多选
    AuthorIDs []string `json:"authorIds"` // 0.4版本调整：编制人改为多选
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OriginalNo string `json:"originalNo,optional"`
    OriginalVersionNo string `json:"originalVersionNo,optional"`
    ReplacementDocID string `json:"replacementDocId,optional"` // 0.5版本新增：替代文件ID
    Remark string `json:"remark,optional"` // 0.5版本新增：备注
}

type CreateInternalDocumentResp {

}



type GetInternalDocumentReq {
    ID string `form:"id"`
}

type GetInternalDocumentResp {
    ID string `json:"id"`
    No string `json:"no"`
    VersionNo string `json:"versionNo"`
    OriginalNo string `json:"originalNo"`
    OriginalVersionNo string `json:"originalVersionNo"`
    Name string `json:"name"`
    EnglishName string `json:"englishName"` // 0.5版本新增：文件英文名称
    DocCategoryID string `json:"docCategoryId"`
    DepartmentIDs []string `json:"departmentIds"` // 0.4版本调整：编制部门改为多选
    DepartmentNames []string `json:"departmentNames"` // 0.4版本调整：编制部门名称
    AuthorIDs []string `json:"authorIds"` // 0.4版本调整：编制人改为多选
    AuthorNicknames []string `json:"authorNicknames"` // 0.4版本调整：编制人姓名
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    ReplacementDocName string `json:"replacementDocName"` // 0.5版本新增：替代文档名称
    ReplacementDocVersion string `json:"replacementDocVersion"` // 0.5版本新增：替代文档版本
    Remark string `json:"remark"` // 0.5版本新增：备注
    Status int `json:"status"`
    ApprovalInfo ApprovalInfo `json:"approvalInfo"`
    FileID string `json:"fileId"`
}






type ChangeInternalDocumentReq {
    ID string `json:"id"`
    Name string `json:"name"`
    EnglishName string `json:"englishName,optional"` // 0.5版本新增：文件英文名称
    FileID string `json:"fileId,optional"`
    DocCategoryID string `json:"docCategoryId"`
    DepartmentIDs []string `json:"departmentIds"` // 0.4版本调整：编制部门改为多选
    AuthorIDs []string `json:"authorIds"` // 0.4版本调整：编制人改为多选
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OriginalNo string `json:"originalNo,optional"`
    OriginalVersionNo string `json:"originalVersionNo,optional"`
    ReplacementDocID string `json:"replacementDocId,optional"` // 0.5版本新增：替代文档ID
    Remark string `json:"remark,optional"` // 0.5版本新增：备注
}

type ChangeInternalDocumentResp {

}



type GetInternalDocumentsReq {
    PageInfo
    DocCategoryIDs []string `json:"docCategoryIds,optional"`
    DepartmentIDs []string `json:"departmentIds,optional"`
    Status int `json:"status,optional"` 
     // 是否有附件
    HasAttachment int8 `json:"hasAttachment,optional"`
    Name string `json:"name,optional"`
    No string `json:"no,optional"`
    OriginalNo string `json:"originalNo,optional"`
}

type GetInternalDocumentsResp {
    PageInfo
    Data []InternalDocumentInfo `json:"data"`
}

type InternalDocumentInfo {
    ID string `json:"id"`
    No string `json:"no"`
    VersionNo string `json:"versionNo"`
    OriginalNo string `json:"originalNo"`
    OriginalVersionNo string `json:"originalVersionNo"`
    Name string `json:"name"`
    EnglishName string `json:"englishName"` // 0.5版本新增：文件英文名称
    DocCategoryID string `json:"docCategoryId"`
    DocCategoryName string `json:"docCategoryName"`
    DepartmentIDs []string `json:"departmentIds"` // 0.4版本调整：编制部门改为多选
    DepartmentNames []string `json:"departmentNames"` // 0.4版本调整：编制部门名称
    AuthorIDs []string `json:"authorIds"` // 0.4版本调整：编制人改为多选
    AuthorNicknames []string `json:"authorNicknames"` // 0.4版本调整：编制人姓名
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    ReplacementDocName string `json:"replacementDocName"` // 0.5版本新增：替代文档名称
    ReplacementDocVersion string `json:"replacementDocVersion"` // 0.5版本新增：替代文档版本
    Remark string `json:"remark"` // 0.5版本新增：备注
    ApprovalInfo ApprovalInfo `json:"approvalInfo"`
    Status int `json:"status"`
}

// ==================== 内部文件变更记录相关接口定义 ====================

// 获取内部文件变更记录请求
type GetInternalDocumentChangeRecordsReq {
  DocumentId string `form:"documentId"` // 文档ID（必需）
  PageInfo
}

// 获取内部文件变更记录响应
type GetInternalDocumentChangeRecordsResp {
  Data []InternalDocumentChangeRecord `json:"data"` // 变更记录列表
  PageInfo
}

// 内部文件变更记录
type InternalDocumentChangeRecord {
  ID string `json:"id"` // 变更记录ID
  DocumentNo string `json:"documentNo"` // 集团文件编号
  DocumentVersion string `json:"documentVersion"` // 版本/版次
  OriginalNo string `json:"originalNo"` // 原文件编号
  OriginalVersion string `json:"originalVersion"` // 原版本/版次
  DocumentName string `json:"documentName"` // 文件名称
  EnglishName string `json:"englishName"` // 0.5版本新增：文件英文名称
  DocumentCategory string `json:"documentCategory"` // 文件类别
  DepartmentNames []string `json:"departmentNames"` // 0.4版本调整：编制部门（多选）
  AuthorNames []string `json:"authorNames"` // 0.4版本调整：编制人（多选）
  ReplacementDocName string `json:"replacementDocName"` // 0.5版本新增：替代文档名称
  ReplacementDocVersion string `json:"replacementDocVersion"` // 0.5版本新增：替代文档版本
  Remark string `json:"remark"` // 0.5版本新增：备注
  Auditors []Approval `json:"auditors"` // 审核人列表（姓名+日期）
  Approvers []Approval `json:"approvers"` // 批准人列表（姓名+日期）
  PublishDate int64 `json:"publishDate"` // 发布日期（毫秒级时间戳）
  EffectiveDate int64 `json:"effectiveDate"` // 实施日期（毫秒级时间戳）
  OperationType int `json:"operationType"` // 操作类型（1-新增、2-修订、3-作废）
  UpdatedAt int64 `json:"updatedAt"` // 更新日期（毫秒级时间戳，用于排序）
}

