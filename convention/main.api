syntax = "v1"

import "base.api"
import "business_dictionary.api"
import "signature.api"
import "book.api"
import "internal_document.api"
import "external_document.api"
import "document_library.api"
import "workflow_pre_callback.api"

@server (
	group:      business_dictionary
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler getBusinessDictionarys
	get /business-dictionary/list (GetBusinessDictionarysReq) returns (GetBusinessDictionarysResp)

	@handler createBusinessDictionaryNode
	post /business-dictionary/node/create (CreateBusinessDictionaryNodeReq) returns (CreateBusinessDictionaryNodeResp)

	@handler getBusinessDictionaryNodes
	get /business-dictionary/node/list (GetBusinessDictionaryNodesReq) returns (GetBusinessDictionaryNodesResp)

	@handler updateBusinessDictionaryNode
	post /business-dictionary/node/update (UpdateBusinessDictionaryNodeReq) returns (UpdateBusinessDictionaryNodeResp)

	@handler moveBusinessDictionaryNode
	post /business-dictionary/node/move (MoveBusinessDictionaryNodeReq) returns (MoveBusinessDictionaryNodeResp)

	@handler deleteBusinessDictionaryNode
	post /business-dictionary/node/delete (DeleteBusinessDictionaryNodeReq) returns (DeleteBusinessDictionaryNodeResp)

	@handler getBusinessDictionaryRelationCount
	get /business-dictionary/node/relation/count (GetBusinessDictionaryRelationCountReq) returns (GetBusinessDictionaryRelationCountResp)

	@handler GetBusinessDictionaryNodeTree
	get /business-dictionary/node/tree (GetBusinessDictionaryNodeTreeReq) returns (GetBusinessDictionaryNodeTreeResp)
}

@server (
	group:      signature
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler CreateSignatureTask
	post /signature-task (CreateSignatureTaskReq) returns (CreateSignatureTaskResp)

	@handler GetSignatureTaskStatus
	get /signature-task/status (GetSignatureTaskStatusReq) returns (GetSignatureTaskStatusResp)

	@handler UploadSignature
	post /signature/upload (UploadSignatureReq) returns (UploadSignatureResp)

	@handler GetCurrentSignature
	get /signatures/current returns (GetCurrentSignatureResp)

	@handler GetSignatureHistory
	get /signatures/history (GetSignatureHistoryReq) returns (GetSignatureHistoryResp)
}

@server (
	group:      file_management_books
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	// 新建书籍信息
	@handler CreateBook
	post /book/create (CreateBookReq) returns (CreateBookResp)

	@handler GetBookList
	post /book/list (GetBookListReq) returns (GetBookListResp)

	@handler UpdateBook
	post /book/update (BookInfo) returns (UpdateBookResp)

	@handler DeleteBook
	post /book/delete (DeleteBookReq) returns (DeleteBookResp)

	@handler ImportBookInfo
	post /book/import (ImportBookReq) returns (ImportBookResp)

	// 归还
	@handler GiveBackBook
	post /book/give-back (GiveBackBookReq) returns (GiveBackBookResp)

	// 查重
	@handler CheckDuplicateBorrowOrReceive
	get /book/plagiarism-check (CheckDuplicateBorrowOrReceiveReq) returns (EmptyResp)
}

@server (
	group:      internaldocument
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler CreateInternalDocument
	post /internal/document/create (CreateInternalDocumentReq) returns (CreateInternalDocumentResp)

	@handler GetInternalDocument
	get /internal/document (GetInternalDocumentReq) returns (GetInternalDocumentResp)

	@handler ChangeInternalDocument
	post /internal/document/change (ChangeInternalDocumentReq) returns (ChangeInternalDocumentResp)

	@handler GetInternalDocuments
	post /internal/documents (GetInternalDocumentsReq) returns (GetInternalDocumentsResp)

	// 获取内部文件变更记录
	@handler GetInternalDocumentChangeRecords
	get /internal/document/change-records (GetInternalDocumentChangeRecordsReq) returns (GetInternalDocumentChangeRecordsResp)

	// ==================== 内部作废文档库相关接口 ====================
	// 获取内部作废文档库列表
	@handler GetInternalDeprecatedDocuments
	post /internal/deprecated-documents (GetInternalDeprecatedDocumentsReq) returns (GetInternalDeprecatedDocumentsResp)

	// 获取内部作废文档详情
	@handler GetInternalDeprecatedDocumentDetail
	get /internal/deprecated-documents/detail (GetInternalDeprecatedDocumentDetailReq) returns (GetInternalDeprecatedDocumentDetailResp)
}

@server (
	group:      externaldocument
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler CreateExternalDocument
	post /external/document/create (CreateExternalDocumentReq) returns (CreateExternalDocumentResp)

	@handler GetExternalDocument
	get /external/document (GetExternalDocumentReq) returns (ExternalDocumentInfo)

	@handler ChangeExternalDocument
	post /external/document/change (ChangeExternalDocumentReq) returns (ChangeExternalDocumentResp)

	@handler GetExternalDocuments
	post /external/documents (GetExternalDocumentsReq) returns (GetExternalDocumentsResp)

	@handler PlagiarismCheck
	post /external/import/company/plagiarism-check (PlagiarismCheckReq) returns (PlagiarismCheckResp)

	// 获取外部文件变更记录
	@handler GetExternalDocumentChangeRecords
	get /external/document/change-records (GetExternalDocumentChangeRecordsReq) returns (GetExternalDocumentChangeRecordsResp)

	// ==================== 外部作废文档库相关接口 ====================
	// 获取外部作废文档库列表
	@handler GetExternalDeprecatedDocuments
	post /external/deprecated-documents (GetExternalDeprecatedDocumentsReq) returns (GetExternalDeprecatedDocumentsResp)

	// 获取外部作废文档详情
	@handler GetExternalDeprecatedDocumentDetail
	get /external/deprecated-documents/detail (GetExternalDeprecatedDocumentDetailReq) returns (GetExternalDeprecatedDocumentDetailResp)
}

@server (
	group:      document_library
	middleware: TraceMiddleware,AuthMiddleware
	prefix:     /nebula/api/v1
)
service nebula {
	@handler ImportDocumentLibrary
	post /document-library/import (ImportDocumentLibraryReq) returns (ImportDocumentLibraryResp)

	@handler ExportDocumentLibrary
	post /document-library/export (ExportDocumentLibraryReq) returns (ExportDocumentLibraryResp)

	// 获取发放清单文档权限关联用户
	@handler GetDocPermissionUsers
	get /document-library/permission/users (GetDocPermissionUsersReq) returns (GetDocPermissionUsersResp)

	// 发放信息暂存
	@handler TemporaryStorageDistributeInfo
	post /document-library/temporary-storage/distribute-info (DistributeApplicationInfoReq) returns (TemporaryStorageDistributeInfoResp)

	// 获取发放列表
	@handler GetDistributeList
	post /document-library/distribute/list (GetDistributeListReq) returns (GetDistributeListResp)

	// 根据发放列表id获取发放清单
	@handler GetDistributeInventoryByID
	get /document-library/distribute/inventory (GetDistributeInventoryReq) returns (GetDistributeInventoryResp)

	//根据发放列表id删除发放信息
	@handler DeleteDistributeByID
	post /document-library/distribute/delete (DeleteDistributeReq) returns (DeleteDistributeResp)

	// 根据发放记录id查询发放详情
	@handler GetDistributeDetail
	get /document-library/distribute-info (GetDistributeDetailReq) returns (GetDistributeDetailResp)

	// 根据发放清单ID查询回收信息
	@handler GetRecycleInfoByDistributeId
	get /document-library/distribute/recycle-info (GetRecycleInfoByDistributeIdReq) returns (GetRecycleInfoByDistributeIdResp)

	// 根据发放清单ID查询处置详情
	@handler GetDisposalDetail
	get /document-library/distribute/disposal-detail (GetDisposalDetailReq) returns (GetDisposalDetailResp)

	// 新增借阅记录
	@handler AddLoan
	post /document-library/loans/add (AddLoanReq) returns (AddLoanResp)

	// 更新借阅记录
	@handler UpdateLoan
	post /document-library/loans/update (UpdateLoanReq) returns (UpdateLoanResp)

	// 查询用户借阅记录
	@handler GetLoanRecords
	get /document-library/loans/records (GetLoanRecordsReq) returns (GetLoanRecordsResp)

	// 查询用户借阅记录-文档清单
	@handler GetLoanRecordDocuments
	get /document-library/loans/record/documents (GetLoanRecordDocumentsReq) returns (GetLoanRecordDocumentsResp)

	// 删除借阅记录
	@handler DeleteLoan
	post /document-library/loans/delete (DeleteLoanReq) returns (DeleteLoanResp)

	// 查询借阅权限
	@handler GetLoanPermissions
	get /document-library/loans/user-permissions (GetLoanPermissionsReq) returns (GetLoanPermissionsResp)

	// 根据文档ID查询发放回收用户记录
	@handler GetDistributeUserPermissions
	get /document-library/document/distribute/user-permissions (GetDistributeUserPermissionsReq) returns (GetDistributeUserPermissionsResp)

	// 权限操作接口
	@handler PermissionOperation
	post /document-library/permission-operation (PermissionOperationReq) returns (PermissionOperationResp)

	// 5.6.2 获取文档发放回收记录
	@handler GetDocumentDistributeRecords
	get /document-library/document/distribute-records (GetDocumentDistributeRecordsReq) returns (GetDocumentDistributeRecordsResp)

	// 5.6.3 获取内发纸质文件一次下载变更记录
	@handler GetInternalPaperDownloadRecords
	get /document-library/document/internal-paper-download-records (GetInternalPaperDownloadRecordsReq) returns (GetInternalPaperDownloadRecordsResp)

	// 5.6.4 获取外发电子文件一次下载变更记录
	@handler GetExternalElectronicDownloadRecords
	get /document-library/document/external-electronic-download-records (GetExternalElectronicDownloadRecordsReq) returns (GetExternalElectronicDownloadRecordsResp)

	// 查询可借阅文档列表
	@handler GetAllowBorrowDocuments
	get /document-library/allow/borrows (GetAllowBorrowDocumentsReq) returns (GetAllowBorrowDocumentsResp)

	// 查询文档的借阅记录
	@handler GetDocumentLoanRecords
	get /document-library/document/loans/records (GetDocumentLoanRecordsReq) returns (GetDocumentLoanRecordsResp)

	// ==================== 作废申请相关接口 ====================
	// 获取作废申请列表
	@handler GetDeprecateApplications
	post /document-library/deprecate-applications (GetDeprecateApplicationsReq) returns (GetDeprecateApplicationsResp)

	// 保存作废申请（新增/编辑/暂存）
	@handler SaveDeprecateApplication
	post /document-library/deprecate-applications/save (SaveDeprecateApplicationReq) returns (SaveDeprecateApplicationResp)

	// 删除作废申请
	@handler DeleteDeprecateApplication
	post /document-library/deprecate-applications/delete (DeleteDeprecateApplicationReq) returns (DeleteDeprecateApplicationResp)

	// 获取作废申请详情
	@handler GetDeprecateApplicationDetail
	get /document-library/deprecate-applications/detail (GetDeprecateApplicationDetailReq) returns (GetDeprecateApplicationDetailResp)

	// 导出作废申请
	@handler ExportDeprecateApplications
	post /document-library/deprecate-applications/export (ExportDeprecateApplicationsReq) returns (ExportDeprecateApplicationsResp)
}

@server (
	group:  document_library
	prefix: /nebula/api/v1
)
service nebula {
	// 工作流前置回调接口：文档库发放
	@handler WorkflowPreCallbackDistribute
	post /workflow/pre-callback/distribute (WorkflowInfoReq) returns (WorkflowInfoResp)

	// 工作流前置回调接口：文档库回收
	@handler WorkflowPreCallbackRecycle
	post /workflow/pre-callback/recycle (WorkflowInfoReq) returns (WorkflowInfoResp)

	// 工作流前置回调接口：文档库处置
	@handler WorkflowPreCallbackDisposal
	post /workflow/post-callback/disposal (WorkflowInfoReq) returns (WorkflowInfoResp)

	// 工作流前置回调接口：外部文档库纳入
	@handler WorkflowPreCallbackIncorporate
	post /workflow/pre-callback/incorporate (WorkflowInfoReq) returns (WorkflowInfoResp)
}

@server (
	group:  file_management_books
	prefix: /nebula/api/v1
)
service nebula {
	// 工作流前置回调接口：借用书籍
	@handler WorkflowPreCallbackBorrow
	post /workflow/pre-callback/borrow-book (WorkflowInfoReq) returns (WorkflowInfoResp)

	// 工作流前置回调接口：领用书籍
	@handler WorkflowPreCallbackReceive
	post /workflow/pre-callback/receive-book (WorkflowInfoReq) returns (WorkflowInfoResp)
}

