package externaldocument

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
)

// TestGetExternalDeprecatedDocuments 测试获取外部作废文档功能
func TestGetExternalDeprecatedDocuments(t *testing.T) {
	convey.Convey("测试获取外部作废文档功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForGetExternalDeprecatedDocuments()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForGetExternalDeprecatedDocuments(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有用户信息的上下文
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_external_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		convey.Convey("测试基本查询功能", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   false,
				},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldBeGreaterThanOrEqualTo, 0)
		})

		convey.Convey("测试按编号过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				Number: "EXT-DEPRECATED-001",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按名称过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				Name: "外部作废文档",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按原文档编号过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				OriginalNumber: "ORIG-EXT-001",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按文档类型过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				TypeDictionaryNodeIds: []string{"test_external_type_001"},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按发文部门过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				PublishDepartment: "测试发文部门",
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试无分页查询", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					NoPage: true,
				},
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试按附件过滤", func() {
			logic := NewGetExternalDeprecatedDocumentsLogic(ctx, svcCtx)

			// 先为一个文档添加附件
			updateCtx := context.Background()
			err := svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
				Model(&mapper.ExternalDocumentLibrary{}).
				Where("id = ?", "test_external_deprecated_doc_001").
				Update("file_id", "test_file_001").Error
			convey.So(err, convey.ShouldBeNil)

			// 测试有附件的文档
			req := &types.GetExternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				BeAttachedFile: 1, // 有附件
			}

			resp, err := logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 1) // 只有1个有附件的文档
			convey.So(len(resp.Data), convey.ShouldEqual, 1)
			convey.So(resp.Data[0].ID, convey.ShouldEqual, "test_external_deprecated_doc_001")

			// 测试无附件的文档
			req.BeAttachedFile = 2 // 无附件
			resp, err = logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 1) // 1个无附件的文档
			convey.So(len(resp.Data), convey.ShouldEqual, 1)

			// 测试全部文档（不过滤附件）
			req.BeAttachedFile = 0 // 全部
			resp, err = logic.GetExternalDeprecatedDocuments(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.Total, convey.ShouldEqual, 2) // 全部2个文档
			convey.So(len(resp.Data), convey.ShouldEqual, 2)
		})
	})
}

// setupTestEnvironmentForGetExternalDeprecatedDocuments 设置获取外部作废文档测试环境
func setupTestEnvironmentForGetExternalDeprecatedDocuments() (*svc.ServiceContext, func(), error) {
	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 初始化gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// createTestDataForGetExternalDeprecatedDocuments 创建获取外部作废文档测试数据
func createTestDataForGetExternalDeprecatedDocuments(svcCtx *svc.ServiceContext) (*TestDataForGetExternalDeprecatedDocuments, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_external_user_001",
			Username:  "test_external_user_001",
			Nickname:  "外部作废文档测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试外部文档
	externalDocs := []mapper.ExternalDocumentLibrary{
		{
			ID:                   "test_external_deprecated_doc_001",
			Number:               "EXT-DEPRECATED-001",
			Version:              "V1.0",
			Name:                 "测试外部作废文档1",
			DocType:              "标准类",
			OriginalNumber:       "ORIG-EXT-001",
			PublishDepartment:    "测试发文部门",
			Status:               -1, // 作废状态
			TypeDictionaryNodeId: "test_external_type_001",
			OrganizationID:       "test_org_001",
			CreatedAt:            now,
			UpdatedAt:            now,
			CreatedBy:            "test_external_user_001",
			UpdatedBy:            "test_external_user_001",
			PublishDate:          now.Add(-365 * 24 * time.Hour), // 一年前发布
			EffectiveDate:        now.Add(-300 * 24 * time.Hour), // 300天前生效
		},
		{
			ID:                   "test_external_deprecated_doc_002",
			Number:               "EXT-DEPRECATED-002",
			Version:              "V1.0",
			Name:                 "测试外部作废文档2",
			DocType:              "规范类",
			OriginalNumber:       "ORIG-EXT-002",
			PublishDepartment:    "测试发文部门",
			Status:               -1, // 作废状态
			TypeDictionaryNodeId: "test_external_type_002",
			OrganizationID:       "test_org_001",
			CreatedAt:            now,
			UpdatedAt:            now,
			CreatedBy:            "test_external_user_001",
			UpdatedBy:            "test_external_user_001",
			PublishDate:          now.Add(-200 * 24 * time.Hour), // 200天前发布
			EffectiveDate:        now.Add(-150 * 24 * time.Hour), // 150天前生效
		},
	}

	// 插入外部文档数据
	for _, doc := range externalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试外部文档失败: %w", err)
		}
	}

	// 3. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_external_type_001",
			Names:  "外部文档类型1",
		},
		{
			NodeID: "test_external_type_002",
			Names:  "外部文档类型2",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	return &TestDataForGetExternalDeprecatedDocuments{
		Users:                           users,
		ExternalDocuments:               externalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
	}, nil
}

// cleanupTestDataForGetExternalDeprecatedDocuments 清理获取外部作废文档测试数据
func cleanupTestDataForGetExternalDeprecatedDocuments(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_external_deprecated_doc_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_external_type_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_external_user_%").Delete(&mapper.User{})
}

// TestDataForGetExternalDeprecatedDocuments 获取外部作废文档测试数据结构
type TestDataForGetExternalDeprecatedDocuments struct {
	Users                           []mapper.User
	ExternalDocuments               []mapper.ExternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
}
