package externaldocument

import (
	"context"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
)

// initServiceContext 初始化测试服务上下文
// 功能：创建测试所需的服务上下文
// 返回值：服务上下文
func initServiceContext() *svc.ServiceContext {
	var c config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &c)

	return &svc.ServiceContext{
		Config:     c,
		DocvaultDB: mapper.NewDocvaultDB(c),
		PhoenixDB:  mapper.NewPhoenixDB(c),
		NebulaDB:   mapper.NewNebulaDB(c),
	}
}

func TestGetExternalDeprecatedDocumentDetailLogic(t *testing.T) {
	<PERSON>vey("GetExternalDeprecatedDocumentDetail", t, func() {
		svcCtx := initServiceContext()

		<PERSON>vey("查询外部作废文档详情", func() {
			// 准备测试数据
			testDoc := createTestExternalDocument(svcCtx)
			defer cleanupTestExternalDocument(svcCtx, testDoc.ID)

			// 构造请求
			req := &types.GetExternalDeprecatedDocumentDetailReq{
				ID: testDoc.ID,
			}

			// 执行逻辑
			logic := NewGetExternalDeprecatedDocumentDetailLogic(context.Background(), svcCtx)
			resp, err := logic.GetExternalDeprecatedDocumentDetail(req)

			// 验证结果
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.ID, ShouldEqual, testDoc.ID)
			So(resp.Number, ShouldEqual, testDoc.Number)
			So(resp.Name, ShouldEqual, testDoc.Name)
			So(resp.DocType, ShouldEqual, testDoc.DocType)
			So(resp.FirstPublishDate, ShouldBeGreaterThan, 0)
			So(resp.FirstEffectiveDate, ShouldBeGreaterThan, 0)
			So(resp.LastDeprecatedDate, ShouldBeGreaterThan, 0)
			So(len(resp.DeprecatedList), ShouldEqual, 1)

			// 验证作废版本详情
			version := resp.DeprecatedList[0]
			So(version.ID, ShouldEqual, testDoc.ID)
			So(version.Number, ShouldEqual, testDoc.Number)
			So(version.Name, ShouldEqual, testDoc.Name)
			So(version.DocType, ShouldEqual, testDoc.DocType)
		})

		Convey("查询多版本外部作废文档详情", func() {
			// 准备主文档
			mainDoc := createTestExternalDocument(svcCtx)
			defer cleanupTestExternalDocument(svcCtx, mainDoc.ID)

			// 准备历史版本文档
			historyDoc := createTestExternalDocumentWithMainID(svcCtx, mainDoc.ID)
			defer cleanupTestExternalDocument(svcCtx, historyDoc.ID)

			// 构造请求
			req := &types.GetExternalDeprecatedDocumentDetailReq{
				ID: mainDoc.ID,
			}

			// 执行逻辑
			logic := NewGetExternalDeprecatedDocumentDetailLogic(context.Background(), svcCtx)
			resp, err := logic.GetExternalDeprecatedDocumentDetail(req)

			// 验证结果
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.ID, ShouldEqual, mainDoc.ID)
			So(len(resp.DeprecatedList), ShouldEqual, 2) // 主文档 + 历史版本

			// 验证时间戳调整
			verifyTimestampAdjustment(resp, []mapper.ExternalDocumentLibrary{mainDoc, historyDoc})
		})

		Convey("查询不存在的文档应返回错误", func() {
			req := &types.GetExternalDeprecatedDocumentDetailReq{
				ID: "non-existent-id",
			}

			logic := NewGetExternalDeprecatedDocumentDetailLogic(context.Background(), svcCtx)
			_, err := logic.GetExternalDeprecatedDocumentDetail(req)

			So(err, ShouldNotBeNil)
		})
	})
}

// createTestExternalDocument 创建测试用的外部文档
func createTestExternalDocument(svcCtx *svc.ServiceContext) mapper.ExternalDocumentLibrary {
	ctx := context.Background()
	client := mapper.NewExternalDocumentLibraryClient(svcCtx.DocvaultDB)

	doc := mapper.ExternalDocumentLibrary{
		ID:                "test-external-doc-id",
		Number:            "TEST-EXT-001",
		Version:           "1.0",
		Name:              "测试外部文档",
		EnglishName:       "Test External Document",
		DocType:           "标准",
		Domain:            "测试领域",
		OriginalDocNumber: "ORIG-001",
		PublishDocNumber:  "PUB-001",
		PublishDepartment: "测试部门",
		PublishDate:       time.Now().AddDate(0, -1, 0),
		EffectiveDate:     time.Now().AddDate(0, 0, -30),
		Status:            -1, // 作废状态
		OrganizationID:    "test-org-id",
		CreatedBy:         "test-user-id",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
		TenantID:          "test-tenant-id",
	}

	err := client.CreateInBatches(ctx, []mapper.ExternalDocumentLibrary{doc})
	if err != nil {
		panic(err)
	}

	return doc
}

// createTestExternalDocumentWithMainID 创建带有主文档ID的测试外部文档
func createTestExternalDocumentWithMainID(svcCtx *svc.ServiceContext, mainID string) mapper.ExternalDocumentLibrary {
	ctx := context.Background()
	client := mapper.NewExternalDocumentLibraryClient(svcCtx.DocvaultDB)

	doc := mapper.ExternalDocumentLibrary{
		ID:                "test-external-doc-history-id",
		MainID:            mainID,
		Number:            "TEST-EXT-001",
		Version:           "0.9",
		Name:              "测试外部文档历史版本",
		EnglishName:       "Test External Document History",
		DocType:           "标准",
		Domain:            "测试领域",
		OriginalDocNumber: "ORIG-001",
		PublishDocNumber:  "PUB-001-OLD",
		PublishDepartment: "测试部门",
		PublishDate:       time.Now().AddDate(0, -2, 0),   // 更早的发布日期
		EffectiveDate:     time.Now().AddDate(0, -1, -30), // 更早的生效日期
		Status:            -1,                             // 作废状态
		OrganizationID:    "test-org-id",
		CreatedBy:         "test-user-id",
		CreatedAt:         time.Now().AddDate(0, 0, -1), // 更早的创建时间
		UpdatedAt:         time.Now().AddDate(0, 0, 1),  // 更晚的更新时间
		TenantID:          "test-tenant-id",
	}

	err := client.CreateInBatches(ctx, []mapper.ExternalDocumentLibrary{doc})
	if err != nil {
		panic(err)
	}

	return doc
}

// cleanupTestExternalDocument 清理测试外部文档
func cleanupTestExternalDocument(svcCtx *svc.ServiceContext, docID string) {
	ctx := context.Background()
	db := svcCtx.DocvaultDB.GetDB()

	db.WithContext(ctx).Where("id = ?", docID).Delete(&mapper.ExternalDocumentLibrary{})
}

// verifyTimestampAdjustment 验证时间戳调整逻辑
func verifyTimestampAdjustment(resp *types.GetExternalDeprecatedDocumentDetailResp, docs []mapper.ExternalDocumentLibrary) {
	// 找出最早的发布日期和生效日期，最晚的作废日期
	var earliestPublish, earliestEffective, latestDeprecated time.Time

	for i, doc := range docs {
		if i == 0 {
			earliestPublish = doc.PublishDate
			earliestEffective = doc.EffectiveDate
			latestDeprecated = doc.UpdatedAt
		} else {
			if doc.PublishDate.Before(earliestPublish) {
				earliestPublish = doc.PublishDate
			}
			if doc.EffectiveDate.Before(earliestEffective) {
				earliestEffective = doc.EffectiveDate
			}
			if doc.UpdatedAt.After(latestDeprecated) {
				latestDeprecated = doc.UpdatedAt
			}
		}
	}

	// 由于数据库时间戳精度问题，使用范围比较
	So(resp.FirstPublishDate, ShouldBeBetween, earliestPublish.UnixMilli()-1000, earliestPublish.UnixMilli()+1000)
	So(resp.FirstEffectiveDate, ShouldBeBetween, earliestEffective.UnixMilli()-1000, earliestEffective.UnixMilli()+1000)
	So(resp.LastDeprecatedDate, ShouldBeBetween, latestDeprecated.UnixMilli()-1000, latestDeprecated.UnixMilli()+1000)
}
