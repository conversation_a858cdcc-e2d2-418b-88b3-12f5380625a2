package internaldocument

import (
	"context"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
)

// setupTestEnvironment 设置测试环境
// 功能：初始化测试所需的服务上下文
// 返回值：服务上下文，清理函数，错误信息
func setupTestEnvironment() (*svc.ServiceContext, func(), error) {
	// 直接从配置文件初始化依赖
	var c config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &c)

	svcCtx := &svc.ServiceContext{
		Config:     c,
		DocvaultDB: mapper.NewDocvaultDB(c),
		PhoenixDB:  mapper.NewPhoenixDB(c),
		NebulaDB:   mapper.NewNebulaDB(c),
	}

	cleanup := func() {
		cleanupTestData(svcCtx)
	}
	return svcCtx, cleanup, nil
}

// cleanupTestData 清理测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_internal_%").Delete(&mapper.InternalDocumentLibrary{})
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Where("id LIKE ?", "test_deprecated_doc_detail_%").Delete(&mapper.InternalDocumentLibrary{})
}

// cleanupAllTestData 清理所有测试数据（包括其他可能存在的测试数据）
// 功能：在测试开始前清理可能存在的测试数据
// 参数：svcCtx - 服务上下文
func cleanupAllTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除所有可能的测试数据
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Where("organization_id = ? AND status = ?", "test_org_001", 3).
		Delete(&mapper.InternalDocumentLibrary{})
}

// getCurrentTime 获取当前时间
// 功能：返回当前时间，用于测试数据创建
// 返回值：当前时间
func getCurrentTime() time.Time {
	return time.Now()
}
