package internaldocument

import (
	"context"
	"testing"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

// TestGetInternalDeprecatedDocumentDetailLogic_GetInternalDeprecatedDocumentDetail 测试获取内部作废文档详情
func TestGetInternalDeprecatedDocumentDetailLogic_GetInternalDeprecatedDocumentDetail(t *testing.T) {
	Convey("测试获取内部作废文档详情功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironment()
		So(err, ShouldBeNil)
		defer cleanup()

		// 清理可能存在的测试数据
		cleanupAllTestData(svcCtx)

		// 创建测试数据
		testDocumentID, err := createTestDeprecatedDocument(svcCtx)
		So(err, ShouldBeNil)
		So(testDocumentID, ShouldNotBeEmpty)
		defer cleanupTestData(svcCtx)

		// 设置上下文
		ctx := context.WithValue(context.Background(), "organizationId", "test_org_001")

		Convey("测试获取文档详情", func() {
			logic := NewGetInternalDeprecatedDocumentDetailLogic(ctx, svcCtx)
			resp, err := logic.GetInternalDeprecatedDocumentDetail(&types.GetInternalDeprecatedDocumentDetailReq{
				ID: testDocumentID,
			})

			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.ID, ShouldEqual, testDocumentID)
			So(resp.DocumentNo, ShouldEqual, "TEST-001")
			So(resp.DocumentName, ShouldEqual, "测试作废文档详情")
			So(resp.FirstPublishDate, ShouldBeGreaterThan, 0)
			So(resp.FirstImplementDate, ShouldBeGreaterThan, 0)
			So(resp.LastDeprecatedDate, ShouldBeGreaterThan, 0)
			So(resp.DeprecatedList, ShouldNotBeNil)
			So(len(resp.DeprecatedList), ShouldBeGreaterThanOrEqualTo, 1)

			// 验证版本详情
			if len(resp.DeprecatedList) > 0 {
				version := resp.DeprecatedList[0]
				So(version.ID, ShouldEqual, testDocumentID)
				So(version.DocumentNo, ShouldEqual, "TEST-001")
				So(version.DocumentName, ShouldEqual, "测试作废文档详情")
			}
		})

		Convey("测试获取不存在的文档详情", func() {
			logic := NewGetInternalDeprecatedDocumentDetailLogic(ctx, svcCtx)
			resp, err := logic.GetInternalDeprecatedDocumentDetail(&types.GetInternalDeprecatedDocumentDetailReq{
				ID: "nonexistent_id",
			})

			So(err, ShouldNotBeNil)
			So(resp, ShouldBeNil)
		})
	})
}

// createTestDeprecatedDocument 创建测试作废文档
// 功能：在数据库中创建一个测试用的作废文档
// 参数：svcCtx - 服务上下文
// 返回值：文档ID，错误信息
func createTestDeprecatedDocument(svcCtx *svc.ServiceContext) (string, error) {
	ctx := context.Background()

	// 创建测试文档
	testDocument := mapper.InternalDocumentLibrary{
		ID:                    "test_deprecated_doc_detail_001",
		MainID:                "",
		OrganizationID:        "test_org_001",
		NoPrefix:              "TEST",
		No:                    "TEST-001",
		SerialNo:              1,
		Name:                  "测试作废文档详情",
		EnglishName:           "Test Deprecated Document Detail",
		FileID:                "",
		DocCategoryID:         "test_category_001",
		DepartmentIDs:         "test_dept_001",
		AuthorIDs:             "test_author_001",
		Status:                3, // 作废状态
		VersionNo:             1,
		Version:               "A/0",
		PublishDate:           getCurrentTime(),
		EffectiveDate:         getCurrentTime(),
		OriginalNo:            "ORIG-001",
		OriginalVersionNo:     "A/0",
		Remark:                "测试备注",
		OperationType:         3, // 作废操作
		CreatedAt:             getCurrentTime(),
		UpdatedAt:             getCurrentTime(),
		CreatedBy:             "test_user_001",
		UpdatedBy:             "test_user_001",
		TenantID:              "test_tenant_001",
		ReplacementDocName:    "",
		ReplacementDocVersion: "",
	}

	// 插入测试数据
	err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&testDocument).Error
	if err != nil {
		return "", err
	}

	return testDocument.ID, nil
}
