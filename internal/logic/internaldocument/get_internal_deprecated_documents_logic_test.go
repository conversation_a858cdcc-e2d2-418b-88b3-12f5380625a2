package internaldocument

import (
	"context"
	"testing"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

// TestData 测试数据结构
// 功能：存储测试过程中创建的数据，便于清理
type TestData struct {
	InternalDocuments []mapper.InternalDocumentLibrary // 内部文档列表
}

// createTestData 创建测试数据
// 功能：创建测试所需的内部作废文档数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据，错误信息
func createTestData(svcCtx *svc.ServiceContext) (*TestData, error) {
	ctx := context.Background()
	now := time.Now()

	// 创建内部作废文档数据
	internalDocuments := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_internal_deprecated_doc_001",
			OrganizationID: "test_org_001",
			NoPrefix:       "INT",
			No:             "INT-001",
			SerialNo:       1,
			Name:           "内部作废文档测试1",
			DocCategoryID:  "test_category_001",
			Status:         3,                     // 作废状态
			PublishDate:    now.AddDate(0, -6, 0), // 6个月前发布
			EffectiveDate:  now.AddDate(0, -5, 0), // 5个月前生效
			CreatedAt:      now.AddDate(0, -6, 0),
			UpdatedAt:      now.AddDate(0, -1, 0), // 1个月前作废
		},
		{
			ID:             "test_internal_deprecated_doc_002",
			OrganizationID: "test_org_001",
			NoPrefix:       "INT",
			No:             "INT-002",
			SerialNo:       2,
			Name:           "内部作废文档测试2",
			DocCategoryID:  "test_category_002",
			Status:         3,                      // 作废状态
			PublishDate:    now.AddDate(0, -12, 0), // 12个月前发布
			EffectiveDate:  now.AddDate(0, -11, 0), // 11个月前生效
			CreatedAt:      now.AddDate(0, -12, 0),
			UpdatedAt:      now.AddDate(0, -2, 0), // 2个月前作废
		},
		{
			ID:             "test_internal_deprecated_doc_003",
			OrganizationID: "test_org_001",
			NoPrefix:       "QUA",
			No:             "QUA-001",
			SerialNo:       1,
			Name:           "质量管理作废文档",
			DocCategoryID:  "test_category_001",
			Status:         3,                     // 作废状态
			PublishDate:    now.AddDate(0, -8, 0), // 8个月前发布
			EffectiveDate:  now.AddDate(0, -7, 0), // 7个月前生效
			CreatedAt:      now.AddDate(0, -8, 0),
			UpdatedAt:      now.AddDate(0, -3, 0), // 3个月前作废
		},
		{
			ID:             "test_internal_active_doc_001",
			OrganizationID: "test_org_001",
			NoPrefix:       "INT",
			No:             "INT-003",
			SerialNo:       3,
			Name:           "内部有效文档测试",
			DocCategoryID:  "test_category_001",
			Status:         1, // 有效状态（不应该在作废文档查询中出现）
			PublishDate:    now.AddDate(0, -3, 0),
			EffectiveDate:  now.AddDate(0, -2, 0),
			CreatedAt:      now.AddDate(0, -3, 0),
			UpdatedAt:      now.AddDate(0, -1, 0),
		},
	}

	// 批量插入内部文档数据
	for _, doc := range internalDocuments {
		err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error
		if err != nil {
			return nil, err
		}
	}

	return &TestData{
		InternalDocuments: internalDocuments,
	}, nil
}

// TestGetInternalDeprecatedDocuments 测试获取内部作废文档功能
func TestGetInternalDeprecatedDocuments(t *testing.T) {
	Convey("测试获取内部作废文档功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironment()
		So(err, ShouldBeNil)
		defer cleanup()

		// 清理可能存在的测试数据
		cleanupAllTestData(svcCtx)

		// 创建测试数据
		testData, err := createTestData(svcCtx)
		So(err, ShouldBeNil)
		So(testData, ShouldNotBeNil)
		defer cleanupTestData(svcCtx)

		// 创建带有组织ID的上下文
		ctx := context.WithValue(context.Background(), "OrganizationId", "test_org_001")

		Convey("测试基本分页查询", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3) // 只有3个作废状态的文档
			So(len(resp.Data), ShouldEqual, 3)

			// 验证返回的都是作废状态的文档
			for _, doc := range resp.Data {
				So(doc.ID, ShouldBeIn, []string{
					"test_internal_deprecated_doc_001",
					"test_internal_deprecated_doc_002",
					"test_internal_deprecated_doc_003",
				})
			}
		})

		Convey("测试按文档编号过滤", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentNo: "INT-001",
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1)
			So(len(resp.Data), ShouldEqual, 1)
			So(resp.Data[0].DocumentNo, ShouldEqual, "INT-001")
			So(resp.Data[0].DocumentName, ShouldEqual, "内部作废文档测试1")
		})

		Convey("测试按文档名称过滤", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentName: "质量管理",
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1)
			So(len(resp.Data), ShouldEqual, 1)
			So(resp.Data[0].DocumentName, ShouldEqual, "质量管理作废文档")
		})

		Convey("测试按文档类别过滤", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentCategoryID: []string{"test_category_001"},
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 2) // test_category_001 有2个作废文档
			So(len(resp.Data), ShouldEqual, 2)
		})

		Convey("测试空结果查询", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentNo: "不存在的编号",
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 0)
			So(len(resp.Data), ShouldEqual, 0)
		})

		Convey("测试组合条件过滤", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				DocumentName:       "内部作废文档",
				DocumentCategoryID: []string{"test_category_001"},
				Status:             3, // 作废状态
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1) // 符合所有条件的只有1个
			So(len(resp.Data), ShouldEqual, 1)
			So(resp.Data[0].DocumentName, ShouldEqual, "内部作废文档测试1")
		})

		Convey("测试分页功能", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			// 第一页，每页2条
			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 2,
				},
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3)     // 总共3条记录
			So(len(resp.Data), ShouldEqual, 2) // 第一页2条记录

			// 第二页，每页2条
			req.Page = 2
			resp, err = logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3)     // 总共3条记录
			So(len(resp.Data), ShouldEqual, 1) // 第二页1条记录
		})

		Convey("测试无分页查询", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
					NoPage:   true, // 不分页
				},
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3)
			So(len(resp.Data), ShouldEqual, 3) // 返回所有记录
		})

		Convey("测试按原文档编号过滤", func() {
			// 先更新一个文档的原文档编号
			updateCtx := context.Background()
			err := svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
				Model(&mapper.InternalDocumentLibrary{}).
				Where("id = ?", "test_internal_deprecated_doc_001").
				Update("original_no", "ORIG-001").Error
			So(err, ShouldBeNil)

			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				OriginalDocumentNo: "ORIG-001",
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1)
			So(len(resp.Data), ShouldEqual, 1)
			So(resp.Data[0].ID, ShouldEqual, "test_internal_deprecated_doc_001")
		})

		Convey("测试按状态过滤", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				Status: 3, // 作废状态
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3) // 3个作废状态的文档
			So(len(resp.Data), ShouldEqual, 3)
		})

		Convey("测试按附件过滤", func() {
			// 先为一个文档添加附件
			updateCtx := context.Background()
			err := svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
				Model(&mapper.InternalDocumentLibrary{}).
				Where("id = ?", "test_internal_deprecated_doc_001").
				Update("file_id", "test_file_001").Error
			So(err, ShouldBeNil)

			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			// 测试有附件的文档
			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				HasAttachment: 1, // 有附件
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 1) // 只有1个有附件的文档
			So(len(resp.Data), ShouldEqual, 1)
			So(resp.Data[0].ID, ShouldEqual, "test_internal_deprecated_doc_001")

			// 测试无附件的文档
			req.HasAttachment = 2 // 无附件
			resp, err = logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 2) // 2个无附件的文档
			So(len(resp.Data), ShouldEqual, 2)

			// 测试全部文档（不过滤附件）
			req.HasAttachment = 0 // 全部
			resp, err = logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3) // 全部3个文档
			So(len(resp.Data), ShouldEqual, 3)
		})

		Convey("测试响应数据结构", func() {
			logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 1,
				},
				DocumentNo: "INT-001",
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(len(resp.Data), ShouldEqual, 1)

			doc := resp.Data[0]
			So(doc.ID, ShouldEqual, "test_internal_deprecated_doc_001")
			So(doc.DocumentNo, ShouldEqual, "INT-001")
			So(doc.DocumentName, ShouldEqual, "内部作废文档测试1")
			// 类别名称查询暂未实现，所以可能为空
			So(doc.FirstPublishDate, ShouldBeGreaterThan, 0)       // 发布日期应该大于0
			So(doc.FirstImplementDate, ShouldBeGreaterThan, 0)     // 实施日期应该大于0
			So(doc.LastDeprecatedDate, ShouldBeGreaterThan, 0)     // 作废日期应该大于0
			So(doc.DeprecatedVersionCount, ShouldBeGreaterThan, 0) // 作废版本数应该大于0
		})

	})
}
