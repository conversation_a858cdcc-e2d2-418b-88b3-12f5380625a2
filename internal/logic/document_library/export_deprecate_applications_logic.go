package document_library

import (
	"context"

	"nebula/internal/logic/document_library/doclib_exporter"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type ExportDeprecateApplicationsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportDeprecateApplicationsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportDeprecateApplicationsLogic {
	return &ExportDeprecateApplicationsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// ExportDeprecateApplications 导出作废申请
// 功能：导出作废申请数据到Excel文件
// 参数：req - 导出请求参数
// 返回值：resp - 导出响应，err - 错误信息
// 异常：参数验证失败、导出器创建失败、导出执行失败
func (l *ExportDeprecateApplicationsLogic) ExportDeprecateApplications(req *types.ExportDeprecateApplicationsReq) (resp *types.ExportDeprecateApplicationsResp, err error) {
	// 实现步骤：
	// 1. 创建作废记录导出器
	// 2. 执行异步导出
	// 3. 返回响应

	// 1. 创建作废记录导出器
	exporter := doclib_exporter.NewExporter(doclib_exporter.NewDeprecationRecordExporter(l.svcCtx), l.svcCtx)

	// 2. 执行异步导出
	err = exporter.Export(l.ctx, req)
	if err != nil {
		logc.Errorf(l.ctx, "导出作废申请失败: %v", err)
		return nil, err
	}

	// 3. 返回响应
	return &types.ExportDeprecateApplicationsResp{}, nil
}
