package document_library

import (
	"context"
	"fmt"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLoanPermissionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLoanPermissionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanPermissionsLogic {
	return &GetLoanPermissionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLoanPermissionsLogic) GetLoanPermissions(req *types.GetLoanPermissionsReq) (resp *types.GetLoanPermissionsResp, err error) {
	// 实现步骤：
	// 1. 获取当前用户ID
	// 2. 查询用户对指定文档的借阅记录
	// 3. 分析借阅权限
	// 4. 如果有借阅权限，查询文档信息获取fileID
	// 5. 返回权限信息和fileID

	// 获取当前用户ID
	userID := utils.GetContextUserID(l.ctx)
	if userID == "" {
		return nil, fmt.Errorf("无法获取当前用户信息")
	}

	// 创建借阅记录查询客户端
	borrowRecordClient := mapper.NewBorrowRecordClient(l.svcCtx.DocvaultDB)

	// 构建查询参数，查询该用户对指定文档的借阅记录
	queryReq := mapper.PageBorrowRecordReq{
		UserID:      userID,
		DocumentIDs: []string{req.DocumentID},
		NoPage:      true, // 不分页，获取所有记录
	}

	// 查询借阅记录
	borrowRecords, _, err := borrowRecordClient.GetBorrowRecordStatistics(l.ctx, queryReq)
	if err != nil {
		return nil, fmt.Errorf("查询借阅记录失败: %w", err)
	}

	// 分析借阅权限
	permission := l.analyzeBorrowPermission(borrowRecords, req.DocumentVersionNo)

	// 构建响应
	resp = &types.GetLoanPermissionsResp{
		Permission: permission,
	}

	// 只有在允许借阅时才返回fileID
	if permission == 1 {
		fileID, err := l.getDocumentFileID(req.DocumentID)
		if err != nil {
			return nil, fmt.Errorf("获取文档文件ID失败: %w", err)
		}
		resp.FileID = fileID
	}

	return resp, nil
}

// getDocumentFileID 获取文档的文件ID
// 功能：通过all_documents_view统一视图查询文档的fileID，避免多次数据库调用
// 参数：
//   - documentID: 文档ID
//
// 返回值：
//   - string: 文件ID
//   - error: 错误信息
func (l *GetLoanPermissionsLogic) getDocumentFileID(documentID string) (string, error) {
	// 使用all_documents_view统一视图查询文档信息
	document, err := mapper.NewAllDocumentsViewClient(l.svcCtx.DocvaultDB).GetDocumentByID(l.ctx, documentID)
	if err != nil {
		return "", fmt.Errorf("查询文档信息失败: %w", err)
	}

	// 返回文档的fileID
	return document.FileID, nil
}

// 借阅权限状态常量
const (
	PermissionAllowed  int32 = 1 // 可借阅
	PermissionPending  int32 = 2 // 有正在审批的数据
	PermissionDenied   int32 = 3 // 不可借阅
	PermissionNotReady int32 = 4 // 借阅时间未到
)

// 审批状态常量
const (
	ApprovalStatusPending  = 2 // 待审批
	ApprovalStatusApproved = 3 // 已审批
	ApprovalStatusRejected = 4 // 已驳回
)

// 借阅状态常量
const (
	BorrowStatusBorrowed = 1 // 已借阅
	BorrowStatusReturned = 3 // 已归还
)

// analyzeBorrowPermission 分析借阅权限
// 功能：根据借阅记录和版本号分析用户的借阅权限
// 参数:
//   - borrowRecords: 借阅记录列表
//   - versionNo: 文档版本号
//
// 返回值:
//   - int32: 借阅权限（1-可借阅，2-有正在审批的数据，3-不可借阅，4-借阅时间未到）
func (l *GetLoanPermissionsLogic) analyzeBorrowPermission(borrowRecords []mapper.BorrowRecordView, versionNo string) int32 {
	// 实现步骤：
	// 1. 基础数据验证
	// 2. 时间有效性检查
	// 3. 审批状态分析
	// 4. 借阅状态检查

	// 基础数据验证
	record := l.validateBasicData(borrowRecords, versionNo)
	if record == nil {
		return PermissionDenied
	}

	// 时间有效性检查
	if timeStatus := l.checkTimeValidity(record); timeStatus != PermissionAllowed {
		return timeStatus
	}

	// 审批状态分析
	return l.analyzeApprovalStatus(record)
}

// validateBasicData 验证基础数据
// 功能：检查借阅记录是否存在以及版本号是否匹配
// 参数:
//   - borrowRecords: 借阅记录列表
//   - versionNo: 文档版本号
//
// 返回值:
//   - *mapper.BorrowRecordView: 有效的借阅记录，如果无效则返回nil
func (l *GetLoanPermissionsLogic) validateBasicData(borrowRecords []mapper.BorrowRecordView, versionNo string) *mapper.BorrowRecordView {
	// 检查是否有借阅记录
	if len(borrowRecords) == 0 {
		return nil
	}

	record := &borrowRecords[0]

	// 检查版本号匹配
	if !l.isVersionMatched(record, versionNo) {
		return nil
	}

	return record
}

// isVersionMatched 检查版本号是否匹配
// 功能：验证借阅记录的版本号是否与请求的版本号匹配
// 参数:
//   - record: 借阅记录
//   - versionNo: 请求的版本号
//
// 返回值:
//   - bool: 版本号是否匹配
func (l *GetLoanPermissionsLogic) isVersionMatched(record *mapper.BorrowRecordView, versionNo string) bool {
	return record.VersionNo != nil && *record.VersionNo == versionNo
}

// checkTimeValidity 检查时间有效性
// 功能：检查借阅时间和归还时间是否在有效范围内
// 参数:
//   - record: 借阅记录
//
// 返回值:
//   - int32: 时间检查结果（1-时间有效，3-已超期，4-借阅时间未到）
func (l *GetLoanPermissionsLogic) checkTimeValidity(record *mapper.BorrowRecordView) int32 {
	now := time.Now()

	// 检查是否已超过归还时间
	if l.isOverdue(record.DueTime, now) {
		return PermissionDenied // 已超过归还时间
	}

	// 检查借阅时间是否未到
	if l.isBorrowTimeNotReady(record.BorrowTime, now) {
		return PermissionNotReady // 借阅时间未到
	}

	return PermissionAllowed // 时间有效
}

// isOverdue 检查是否已超期
// 功能：判断当前时间是否已超过归还时间
// 参数:
//   - dueTime: 归还时间
//   - now: 当前时间
//
// 返回值:
//   - bool: 是否已超期
func (l *GetLoanPermissionsLogic) isOverdue(dueTime time.Time, now time.Time) bool {
	return now.After(dueTime)
}

// isBorrowTimeNotReady 检查借阅时间是否未到
// 功能：判断借阅时间是否还未到达
// 参数:
//   - borrowTime: 借阅时间
//   - now: 当前时间
//
// 返回值:
//   - bool: 借阅时间是否未到
func (l *GetLoanPermissionsLogic) isBorrowTimeNotReady(borrowTime time.Time, now time.Time) bool {
	return borrowTime.After(now)
}

// analyzeApprovalStatus 分析审批状态
// 功能：根据审批状态和借阅状态确定最终的借阅权限
// 参数:
//   - record: 借阅记录
//
// 返回值:
//   - int32: 借阅权限状态
func (l *GetLoanPermissionsLogic) analyzeApprovalStatus(record *mapper.BorrowRecordView) int32 {
	switch record.ApprovalStatus {
	case ApprovalStatusPending:
		return PermissionPending // 有正在审批的数据

	case ApprovalStatusApproved:
		return l.checkBorrowStatus(record)

	case ApprovalStatusRejected:
		return PermissionAllowed // 已驳回记录不影响借阅权限

	default:
		return PermissionAllowed // 默认可借阅
	}
}

// checkBorrowStatus 检查借阅状态
// 功能：对于已审批的记录，检查具体的借阅状态
// 参数:
//   - record: 借阅记录
//
// 返回值:
//   - int32: 借阅权限状态
func (l *GetLoanPermissionsLogic) checkBorrowStatus(record *mapper.BorrowRecordView) int32 {
	// 如果文档已被归还，不可借阅
	if l.isDocumentReturned(record) {
		return PermissionDenied
	}

	return PermissionAllowed
}

// isDocumentReturned 检查文档是否已归还
// 功能：判断文档是否已经被归还
// 参数:
//   - record: 借阅记录
//
// 返回值:
//   - bool: 文档是否已归还
func (l *GetLoanPermissionsLogic) isDocumentReturned(record *mapper.BorrowRecordView) bool {
	return record.BorrowStatus != nil && *record.BorrowStatus == BorrowStatusReturned
}
