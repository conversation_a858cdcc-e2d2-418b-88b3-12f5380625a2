package document_library

import (
	"context"
	"errors"

	"nebula/internal/logic/document_library/doclib_exporter"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ExportDocumentLibraryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportDocumentLibraryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportDocumentLibraryLogic {
	return &ExportDocumentLibraryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportDocumentLibraryLogic) ExportDocumentLibrary(req *types.ExportDocumentLibraryReq) (resp *types.ExportDocumentLibraryResp, err error) {
	var exporter doclib_exporter.Exporter
	switch req.ModuleType {
	case 1: // 书籍库
		exporter = doclib_exporter.NewExporter(doclib_exporter.NewBookDocumentExporter(l.svcCtx), l.svcCtx)
	case 2: // 内部库
		exporter = doclib_exporter.NewExporter(doclib_exporter.NewInternalDocumentExporter(l.svcCtx), l.svcCtx)
	case 3: // 外部库
		exporter = doclib_exporter.NewExporter(doclib_exporter.NewExternalDocumentExporter(l.svcCtx), l.svcCtx)
	case 4: // 发放记录
		exporter = doclib_exporter.NewExporter(doclib_exporter.NewDistributeExporter(l.svcCtx), l.svcCtx)
	case 5: // 借阅记录
		exporter = doclib_exporter.NewExporter(doclib_exporter.NewBorrowRecordExporter(l.svcCtx), l.svcCtx)
	default:
		return nil, errors.New("module type not supported")
	}
	err = exporter.Export(l.ctx, req.Params)
	if err != nil {
		return nil, err
	}

	return
}
