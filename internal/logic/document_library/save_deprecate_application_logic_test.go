package document_library

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
)

// TestSaveDeprecateApplication 测试保存作废申请功能
func TestSaveDeprecateApplication(t *testing.T) {
	convey.Convey("测试保存作废申请功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForSaveDeprecateApplication()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForSaveDeprecateApplication(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有用户信息的上下文
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_deprecate_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		convey.Convey("测试创建新的作废申请", func() {
			// 清理可能存在的数据
			cleanupTestDataForSaveDeprecateApplication(svcCtx)
			logic := NewSaveDeprecateApplicationLogic(ctx, svcCtx)

			// 测试创建作废申请
			req := &types.SaveDeprecateApplicationReq{
				PlannedDeprecateDate: time.Now().AddDate(0, 1, 0).Unix(), // 一个月后
				DocumentModuleType:   2, // 内部文档
				DocumentCategoryID:   "test_deprecate_category_001",
				DeprecateReason:      1, // 版本过期
				OtherReason:          "",
				DeprecateList: []types.DeprecateDocumentItem{
					{
						DocumentID: "test_deprecate_internal_doc_001",
					},
				},
			}

			resp, err := logic.SaveDeprecateApplication(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.ID, convey.ShouldNotBeEmpty)

			// 直接通过 SQL 查询验证结果
			verifyDeprecationRecordInDatabase(svcCtx, resp.ID, 1, "")
			verifyDeprecationDocumentRelationsInDatabase(svcCtx, resp.ID, []string{"test_deprecate_internal_doc_001"})
		})

		convey.Convey("测试创建带有其他原因的作废申请", func() {
			// 清理可能存在的数据
			cleanupTestDataForSaveDeprecateApplication(svcCtx)
			logic := NewSaveDeprecateApplicationLogic(ctx, svcCtx)

			req := &types.SaveDeprecateApplicationReq{
				PlannedDeprecateDate: time.Now().AddDate(0, 2, 0).Unix(), // 两个月后
				DocumentModuleType:   2, // 内部文档
				DocumentCategoryID:   "test_deprecate_category_001",
				DeprecateReason:      9, // 其他原因
				OtherReason:          "@test@业务流程重大变更，文档内容不再适用",
				DeprecateList: []types.DeprecateDocumentItem{
					{
						DocumentID: "test_deprecate_internal_doc_002",
					},
				},
			}

			resp, err := logic.SaveDeprecateApplication(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
			convey.So(resp.ID, convey.ShouldNotBeEmpty)

			// SQL 验证
			verifyDeprecationRecordInDatabase(svcCtx, resp.ID, 9, "@test@业务流程重大变更，文档内容不再适用")
			verifyDeprecationDocumentRelationsInDatabase(svcCtx, resp.ID, []string{"test_deprecate_internal_doc_002"})
		})

		convey.Convey("测试更新现有作废申请", func() {
			// 清理可能存在的数据
			cleanupTestDataForSaveDeprecateApplication(svcCtx)
			logic := NewSaveDeprecateApplicationLogic(ctx, svcCtx)

			// 先创建一个作废申请
			createReq := &types.SaveDeprecateApplicationReq{
				PlannedDeprecateDate: time.Now().AddDate(0, 1, 0).Unix(),
				DocumentModuleType:   2,
				DocumentCategoryID:   "test_deprecate_category_001",
				DeprecateReason:      1,
				OtherReason:          "",
				DeprecateList: []types.DeprecateDocumentItem{
					{
						DocumentID: "test_deprecate_internal_doc_001",
					},
				},
			}

			createResp, err := logic.SaveDeprecateApplication(createReq)
			convey.So(err, convey.ShouldBeNil)
			convey.So(createResp.ID, convey.ShouldNotBeEmpty)

			// 更新作废申请
			updateReq := &types.SaveDeprecateApplicationReq{
				ID:                   createResp.ID, // 设置ID表示更新
				PlannedDeprecateDate: time.Now().AddDate(0, 3, 0).Unix(), // 改为三个月后
				DocumentModuleType:   2,
				DocumentCategoryID:   "test_deprecate_category_001",
				DeprecateReason:      2, // 改为功能变更
				OtherReason:          "",
				DeprecateList: []types.DeprecateDocumentItem{
					{
						DocumentID: "test_deprecate_internal_doc_001",
					},
				},
			}

			updateResp, err := logic.SaveDeprecateApplication(updateReq)
			convey.So(err, convey.ShouldBeNil)
			convey.So(updateResp.ID, convey.ShouldEqual, createResp.ID)

			// SQL 验证更新结果
			verifyDeprecationRecordInDatabase(svcCtx, updateResp.ID, 2, "")
			verifyDeprecationDocumentRelationsInDatabase(svcCtx, updateResp.ID, []string{"test_deprecate_internal_doc_001"})
		})

		convey.Convey("测试多文档作废申请", func() {
			// 清理可能存在的数据
			cleanupTestDataForSaveDeprecateApplication(svcCtx)
			logic := NewSaveDeprecateApplicationLogic(ctx, svcCtx)

			req := &types.SaveDeprecateApplicationReq{
				PlannedDeprecateDate: time.Now().AddDate(0, 1, 0).Unix(),
				DocumentModuleType:   2,
				DocumentCategoryID:   "test_deprecate_category_001",
				DeprecateReason:      3, // 政策变更
				OtherReason:          "",
				DeprecateList: []types.DeprecateDocumentItem{
					{
						DocumentID: "test_deprecate_internal_doc_001",
					},
					{
						DocumentID: "test_deprecate_internal_doc_002",
					},
				},
			}

			resp, err := logic.SaveDeprecateApplication(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)

			// SQL 验证多文档关系
			verifyDeprecationDocumentRelationsInDatabase(svcCtx, resp.ID, []string{"test_deprecate_internal_doc_001", "test_deprecate_internal_doc_002"})
		})

		convey.Convey("测试参数验证", func() {
			logic := NewSaveDeprecateApplicationLogic(ctx, svcCtx)

			convey.Convey("测试拟定作废日期为空", func() {
				req := &types.SaveDeprecateApplicationReq{
					PlannedDeprecateDate: 0, // 空日期
					DocumentModuleType:   2,
					DocumentCategoryID:   "test_deprecate_category_001",
					DeprecateReason:      1,
					DeprecateList: []types.DeprecateDocumentItem{
						{DocumentID: "test_deprecate_internal_doc_001"},
					},
				}

				resp, err := logic.SaveDeprecateApplication(req)
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(resp, convey.ShouldBeNil)
				convey.So(err.Error(), convey.ShouldContainSubstring, "拟定作废日期不能为空")
			})

			convey.Convey("测试选择其他原因但未填写描述", func() {
				req := &types.SaveDeprecateApplicationReq{
					PlannedDeprecateDate: time.Now().Unix(),
					DocumentModuleType:   2,
					DocumentCategoryID:   "test_deprecate_category_001",
					DeprecateReason:      9, // 其他原因
					OtherReason:          "", // 但未填写描述
					DeprecateList: []types.DeprecateDocumentItem{
						{DocumentID: "test_deprecate_internal_doc_001"},
					},
				}

				resp, err := logic.SaveDeprecateApplication(req)
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(resp, convey.ShouldBeNil)
				convey.So(err.Error(), convey.ShouldContainSubstring, "选择其他原因时，必须填写具体原因描述")
			})

			convey.Convey("测试作废清单为空", func() {
				req := &types.SaveDeprecateApplicationReq{
					PlannedDeprecateDate: time.Now().Unix(),
					DocumentModuleType:   2,
					DocumentCategoryID:   "test_deprecate_category_001",
					DeprecateReason:      1,
					DeprecateList:        []types.DeprecateDocumentItem{}, // 空清单
				}

				resp, err := logic.SaveDeprecateApplication(req)
				convey.So(err, convey.ShouldNotBeNil)
				convey.So(resp, convey.ShouldBeNil)
				convey.So(err.Error(), convey.ShouldContainSubstring, "作废清单不能为空")
			})
		})
	})
}

// setupTestEnvironmentForSaveDeprecateApplication 设置保存作废申请测试环境
func setupTestEnvironmentForSaveDeprecateApplication() (*svc.ServiceContext, func(), error) {
	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 初始化gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForSaveDeprecateApplication(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// createTestDataForSaveDeprecateApplication 创建保存作废申请测试数据
func createTestDataForSaveDeprecateApplication(svcCtx *svc.ServiceContext) (*TestDataForSaveDeprecateApplication, error) {
	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForSaveDeprecateApplication(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_deprecate_user_001",
			Username:  "test_deprecate_user_001",
			Nickname:  "作废申请测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_deprecate_internal_doc_001",
			No:             "DEPRECATE-INT-001",
			Name:           "作废申请测试内部文档1",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			FileID:         "test_deprecate_file_001",
			DocCategoryID:  "test_deprecate_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_deprecate_user_001",
			CreatedBy:      "test_deprecate_user_001",
			UpdatedBy:      "test_deprecate_user_001",
			EffectiveDate:  now.Add(30 * 24 * time.Hour),
		},
		{
			ID:             "test_deprecate_internal_doc_002",
			No:             "DEPRECATE-INT-002",
			Name:           "作废申请测试内部文档2",
			VersionNo:      1,
			Status:         3, // 有效状态
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			TenantID:       "test_tenant_001",
			FileID:         "test_deprecate_file_002",
			DocCategoryID:  "test_deprecate_category_001",
			DepartmentIDs:  "test_dept_001",
			AuthorIDs:      "test_deprecate_user_001",
			CreatedBy:      "test_deprecate_user_001",
			UpdatedBy:      "test_deprecate_user_001",
			EffectiveDate:  now.Add(60 * 24 * time.Hour),
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_deprecate_category_001",
			Names:  "作废申请内部文档类别",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	return &TestDataForSaveDeprecateApplication{
		Users:                           users,
		InternalDocuments:               internalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
	}, nil
}

// cleanupTestDataForSaveDeprecateApplication 清理保存作废申请测试数据
func cleanupTestDataForSaveDeprecateApplication(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除作废文档关系记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_document_relations WHERE document_id LIKE '%test_deprecate_%'")

	// 删除作废记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM deprecation_records WHERE other_reason LIKE '%@test@%' OR created_by = 'test_deprecate_user_001'")

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_deprecate_category_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_deprecate_user_%").Delete(&mapper.User{})
}

// verifyDeprecationRecordInDatabase 直接查询数据库验证作废记录
func verifyDeprecationRecordInDatabase(svcCtx *svc.ServiceContext, deprecationID string, expectedReason int32, expectedOtherReason string) {
	ctx := context.Background()
	deprecationClient := mapper.NewDeprecationRecordClient(svcCtx.DocvaultDB)

	record, err := deprecationClient.GetByID(ctx, deprecationID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(record, convey.ShouldNotBeNil)

	convey.So(record.OrganizationID, convey.ShouldEqual, "test_org_001")
	convey.So(record.TenantID, convey.ShouldEqual, "test_tenant_001")
	convey.So(record.CreatedBy, convey.ShouldEqual, "test_deprecate_user_001")
	convey.So(record.Reason, convey.ShouldEqual, expectedReason)
	convey.So(record.OtherReason, convey.ShouldEqual, expectedOtherReason)
	convey.So(record.ApprovalStatus, convey.ShouldEqual, int32(1)) // 待提交状态
}

// verifyDeprecationDocumentRelationsInDatabase 直接查询数据库验证作废文档关系
func verifyDeprecationDocumentRelationsInDatabase(svcCtx *svc.ServiceContext, deprecationID string, expectedDocuments []string) {
	ctx := context.Background()
	relationClient := mapper.NewDeprecationDocumentRelationClient(svcCtx.DocvaultDB)

	relations, err := relationClient.GetByDeprecationRecordID(ctx, deprecationID)
	convey.So(err, convey.ShouldBeNil)
	convey.So(len(relations), convey.ShouldEqual, len(expectedDocuments))

	// 验证文档ID
	actualDocuments := make([]string, len(relations))
	for i, relation := range relations {
		actualDocuments[i] = relation.DocumentID
	}

	for _, expectedDoc := range expectedDocuments {
		found := false
		for _, actualDoc := range actualDocuments {
			if actualDoc == expectedDoc {
				found = true
				break
			}
		}
		convey.So(found, convey.ShouldBeTrue)
	}
}

// TestDataForSaveDeprecateApplication 保存作废申请测试数据结构
type TestDataForSaveDeprecateApplication struct {
	Users                           []mapper.User
	InternalDocuments               []mapper.InternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
}

//