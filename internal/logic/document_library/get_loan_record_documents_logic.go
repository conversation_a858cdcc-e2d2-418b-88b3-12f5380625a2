package document_library

import (
	"context"
	"fmt"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLoanRecordDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLoanRecordDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanRecordDocumentsLogic {
	return &GetLoanRecordDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetLoanRecordDocuments 获取借阅记录文档清单
// 功能: 根据借阅记录ID查询该记录下的所有文档信息
// 参数: req - 查询请求参数，包含借阅记录ID
// 返回值: resp - 文档清单响应，err - 错误信息
// 异常: 当借阅记录ID为空或查询失败时返回错误
func (l *GetLoanRecordDocumentsLogic) GetLoanRecordDocuments(req *types.GetLoanRecordDocumentsReq) (resp *types.GetLoanRecordDocumentsResp, err error) {
	// 实现步骤：
	// 1. 验证请求参数
	// 2. 使用左连接查询直接获取借阅文档关系和详情（优化后的单次查询）
	// 3. 批量查询类别名称
	// 4. 转换数据格式为响应结构
	// 5. 返回结果

	// 验证请求参数
	if req.Id == "" {
		return nil, fmt.Errorf("借阅记录ID不能为空")
	}

	// 使用左连接查询直接获取借阅文档关系和详情（减少查询次数）
	documentsWithDetails, err := mapper.NewBorrowRecordClient(l.svcCtx.DocvaultDB).GetDocumentsWithDetailsByBorrowRecordID(l.ctx, req.Id)
	if err != nil {
		l.Logger.Errorf("查询借阅记录文档详情失败: %v", err)
		return nil, fmt.Errorf("查询借阅记录文档详情失败: %w", err)
	}

	// 转换为响应格式
	loanDocuments, err := l.convertDocumentsWithDetailsToLoanDocuments(documentsWithDetails)
	if err != nil {
		l.Logger.Errorf("转换文档数据失败: %v", err)
		return nil, fmt.Errorf("转换文档数据失败: %w", err)
	}

	// 构造响应
	resp = &types.GetLoanRecordDocumentsResp{
		Data: loanDocuments,
	}

	return resp, nil
}

// convertModuleTypeToChineseName 将模块类型转换为中文名称
// 1：书籍，2：内部文档，3：外部文档
func (l *GetLoanRecordDocumentsLogic) convertModuleTypeToChineseName(moduleType int) string {
	switch moduleType {
	case 1:
		return "书籍"
	case 2:
		return "内部文档"
	case 3:
		return "外部文档"
	default:
		return "未知类型"
	}
}

// convertDocumentsWithDetailsToLoanDocuments 转换BorrowDocumentWithDetails到LoanDocument
// 功能: 将左连接查询结果转换为响应格式，并批量查询类别名称
// 参数: documentsWithDetails - 借阅文档详情列表
// 返回值: []types.LoanDocument - 借阅文档列表，error - 错误信息
// 异常: 当查询类别信息失败时返回错误
func (l *GetLoanRecordDocumentsLogic) convertDocumentsWithDetailsToLoanDocuments(documentsWithDetails []mapper.BorrowDocumentWithDetails) ([]types.LoanDocument, error) {
	// 实现步骤：
	// 1. 收集类别ID并去重
	// 2. 批量查询类别名称
	// 3. 转换数据格式
	// 4. 返回结果

	if len(documentsWithDetails) == 0 {
		return []types.LoanDocument{}, nil
	}

	// 收集类别ID并去重
	categoryIDSet := make(map[string]bool)
	uniqueCategories := make([]string, 0)
	for _, doc := range documentsWithDetails {
		if doc.DocumentCategoryID != "" && !categoryIDSet[doc.DocumentCategoryID] {
			categoryIDSet[doc.DocumentCategoryID] = true
			uniqueCategories = append(uniqueCategories, doc.DocumentCategoryID)
		}
	}

	// 批量查询类别名称
	categoryMap := make(map[string]string)
	if len(uniqueCategories) > 0 {
		businessDictionaryClient := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB)
		categoryRelations, err := businessDictionaryClient.GetBusinessDictionaryNodeRelationByNodeIDs(l.ctx, uniqueCategories)
		if err != nil {
			logx.Errorf("查询类别名称失败: %v", err)
			// 不中断流程，继续处理
		} else {
			for _, relation := range categoryRelations {
				categoryMap[relation.NodeID] = relation.Names
			}
		}
	}

	// 转换数据格式
	loanDocuments := make([]types.LoanDocument, 0, len(documentsWithDetails))
	for _, doc := range documentsWithDetails {
		// 获取模块类型中文名称
		moduleTypeName := l.convertModuleTypeToChineseName(doc.ModuleType)

		// 获取类别名称
		categoryName := categoryMap[doc.DocumentCategoryID]
		if categoryName == "" {
			categoryName = "未分类"
		}

		loanDocument := types.LoanDocument{
			DocumentId:           doc.DocumentID,
			DocumentName:         doc.DocumentName,
			DocumentValidity:     3, // 默认设置为有效
			DocumentModuleType:   int32(doc.ModuleType),
			DocumentModuleName:   moduleTypeName,
			DocumentCategoryID:   doc.DocumentCategoryID,
			DocumentCategoryName: categoryName,
			DocumentNo:           doc.DocumentNo,
			DocumentVersionNo:    doc.VersionNo,
			BorrowStatus:         int32(doc.BorrowStatus),
		}

		loanDocuments = append(loanDocuments, loanDocument)
	}

	return loanDocuments, nil
}
