package document_library

import (
	"context"
	"errors"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

const CODE = "not permission"

type GetDistributeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeListLogic {
	return &GetDistributeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDistributeListLogic) GetDistributeList(req *types.GetDistributeListReq) (resp *types.GetDistributeListResp, err error) {
	// 使用 Applicant 模糊搜索申请人ids，如果登录用户是普通文件用户，则只差当前用户的申请记录
	applicant, err := l.getApplicant(req.Applicant)
	if err != nil && err.Error() == CODE {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	// 获取发放列表
	list, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDistributeInfoList(l.ctx, &docvault.GetDistributeListReq{
		PageInfo: &docvault.PageInfo{
			Page:     int32(req.Page),
			PageSize: int32(req.PageSize),
			NoPage:   req.NoPage,
		},
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       int32(req.FileType),
		FileCategory:   req.FileCategory,
		DistributeType: int32(req.DistributeType),
		Status:         int32(req.Status),
		Applicant:      applicant,
	})
	if err != nil {
		l.Logger.Errorf("获取发放信息列表失败: %v", err)
		return nil, err
	}

	resp = l.dataTransition(list)
	resp.Total = list.Total

	return resp, nil
}

func (l *GetDistributeListLogic) getApplicant(userName string) ([]string, error) {
	var ids []string
	// 如果申请人不为空，则使用申请人查询，使用模糊查询查询用户id
	if userName != "" {
		// ids 添加默认值，防止使用查询条件查询出用户为空时，使用了空条件查询出所有数据
		ids = append(ids, "1")
		users, err := l.svcCtx.PhoenixClient.GetUserInfoByNickname(l.ctx, userName)
		if err != nil {
			l.Logger.Errorf("获取用户信息失败: %v", err)
			return nil, err
		}
		for _, user := range users {
			ids = append(ids, user.ID)
		}
	}
	// 查询用户登录信息
	userLoginInfo := utils.GetCurrentLoginUser(l.ctx)
	// 查询登录组织信息
	organization, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, userLoginInfo.OrganizationId)
	if err != nil {
		l.Logger.Errorf("获取组织信息失败: %v", err)
		return nil, err
	}
	code := false
	if organization.ParentId == "" { // 集团
		// 先判断是否集团文件管理员，是则使用查询条件
		code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "JTWJGLY")
		if err != nil {
			l.Logger.Errorf("获取用户角色失败: %v", err)
			return nil, err
		}
		if code {
			return ids, nil
		}
		// 再判断是否集团普通用户，是则查询自己申请的记录
		code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "JTWJYH")
		if err != nil {
			l.Logger.Errorf("获取用户角色失败: %v", err)
			return nil, err
		}
		if code {
			ids = []string{userLoginInfo.UserId}
			return ids, nil
		}
	} else { // 子公司
		// 先判断是否子公司文件管理员，是则使用查询条件
		code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "ZGSWJGLY")
		if err != nil {
			l.Logger.Errorf("获取用户角色失败: %v", err)
			return nil, err
		}
		if code {
			return ids, nil
		}
		// 再判断是否子公司普通用户，是则只查询自己的申请记录
		code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "ZGSWJYH")
		if err != nil {
			l.Logger.Errorf("获取用户角色失败: %v", err)
			return nil, err
		}
		if code {
			ids = []string{userLoginInfo.UserId}
			return ids, nil
		}
	}
	return nil, errors.New(CODE)
}

func (l *GetDistributeListLogic) dataTransition(list *docvault.GetDistributeListResp) *types.GetDistributeListResp {
	var getDistributeListInfo []types.GetDistributeListInfo
	for _, v := range list.Data {
		distributeCount := 0                       // 发放份数
		approvalInfo := types.ApprovalInfo{}       // 审批人
		recycleStatus := ""                        // 回收状态
		var received []types.DistributeUser        // 已签收人
		var notReceived []types.DistributeUser     // 未签收人
		var recycle []types.DistributeUser         // 已回收人
		var disposalBy []types.DistributeUser      // 已处置人
		var distributeUsers []types.DistributeUser // 用户汇总
		paperDocumentStatus := 1                   // 纸质文档状态 1没有已回收 | 2有已回收
		paperDocumentRecycleCount := 0             // 纸质文档回收份数
		// 已审批，获取接收人信息
		if v.Status == 3 {
			distributeCount = int(v.DistributeCount)
			// 已签收
			for _, user := range v.Received {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   1,
				}
				received = append(received, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 未签收
			for _, user := range v.NotReceived {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   2,
				}
				notReceived = append(notReceived, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 已回收
			for _, user := range v.Recycle {
				if user.FileForm == 2 {
					paperDocumentStatus = 2
					paperDocumentRecycleCount++
				}
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   3,
				}
				recycle = append(recycle, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 已处置
			for _, user := range v.DisposalBy {
				temp := types.DistributeUser{
					UserID:   user.UserId,
					FileForm: user.FileForm,
					Nickname: user.Nickname,
					Status:   4,
				}
				disposalBy = append(disposalBy, temp)
				distributeUsers = append(distributeUsers, temp)
			}
			// 全部已处置
			if paperDocumentStatus == 2 && len(v.DisposalBy) == paperDocumentRecycleCount {
				paperDocumentStatus = 3
			}

			if len(recycle) == 0 {
				recycleStatus = "未回收"
			} else if len(recycle) > 0 && len(recycle) < int(v.DistributeCount) {
				recycleStatus = "部分回收"
			} else {
				recycleStatus = "已回收"
			}
		}
		// 已审批和已驳回，获取审批人
		if v.Status == 4 || v.Status == 3 {
			if v.ApprovalInfo != nil {
				for _, r := range v.ApprovalInfo.Approvers {
					approvalInfo.Approvers = append(approvalInfo.Approvers, types.Approval{
						UserID:       r.UserId,
						PassedDate:   r.PassedDate,
						UserNickname: r.Nickname,
					})
				}
				for _, r := range v.ApprovalInfo.Auditors {
					approvalInfo.Auditors = append(approvalInfo.Auditors, types.Approval{
						UserID:       r.UserId,
						PassedDate:   r.PassedDate,
						UserNickname: r.Nickname,
					})
				}
			}
		}
		getDistributeListInfo = append(getDistributeListInfo, types.GetDistributeListInfo{
			ID:                  v.Id,
			Applicant:           v.Applicant,
			ApplyDate:           v.ApplyDate,
			DistributeType:      int(v.DistributeType),
			FileType:            int(v.FileType),
			FileCategory:        v.FileCategory,
			Reason:              v.Reason,
			OtherReason:         v.OtherReason,
			WishDistributeDate:  v.WishDistributeDate,
			Status:              int(v.Status),
			WorkflowID:          v.WorkflowId,
			ApprovalInfo:        approvalInfo,
			DistributeCount:     distributeCount,
			DisposalCount:       len(disposalBy),
			ReceivedCount:       len(received),
			DistributeUsers:     distributeUsers,
			RecycleStatus:       recycleStatus,
			PaperDocumentStatus: paperDocumentStatus,
		})
	}
	return &types.GetDistributeListResp{
		Data: getDistributeListInfo,
	}
}
