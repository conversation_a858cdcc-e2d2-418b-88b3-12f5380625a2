package mapper

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"

	"gorm.io/gorm"
)

const (
	TableNameDistributeRecordPermission = "distribute_record_permissions"
)

// DistributeRecordPermission 对应 distribute_record_permissions 表
type DistributeRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联DistributeRecordFile的ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
	Recipient      string `gorm:"type:varchar(255);comment:'接收方'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	UserName       string `gorm:"type:varchar(255);comment:'接收人姓名'"`
	SignForStatus  int32  `gorm:"comment:'签收状态,1未签收 | 2已签收'"`
	DisposeStatus  int32  `gorm:"comment:'处置状态,1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 ｜5已处置'"`
	IsUsed         bool   `gorm:"comment:'是否已使用,使用后变为true'"`
}

func (DistributeRecordPermission) TableName() string {
	return TableNameDistributeRecordPermission
}

// DistributeRecordPermissionClient 是 distribute_record_permissions 表的数据访问客户端
type DistributeRecordPermissionClient struct {
	db *gorm.DB
}

// NewDistributeRecordPermissionClient 创建一个新的 DistributeRecordPermissionClient 实例
func NewDistributeRecordPermissionClient(db *DocvaultDB) *DistributeRecordPermissionClient {
	return &DistributeRecordPermissionClient{
		db: db.GetDB(),
	}
}

// DistributeRecordPermissionWithStatus 权限记录包含发放记录状态
type DistributeRecordPermissionWithStatus struct {
	DistributeRecordPermission
	DistributeRecordStatus int32 `gorm:"column:distribute_record_status"` // 发放记录状态
}

// FindByFileRecordIDs 根据文件记录ID列表查询权限记录
// 功能: 查询指定文件记录ID列表对应的所有权限记录
// 参数:
//   - ctx: 上下文
//   - fileRecordIDs: 文件记录ID列表
//
// 返回值:
//   - permissions: 权限记录列表
//   - err: 错误信息
func (c *DistributeRecordPermissionClient) FindByFileRecordIDs(ctx context.Context, fileRecordIDs []string) ([]DistributeRecordPermission, error) {
	var permissions []DistributeRecordPermission
	if len(fileRecordIDs) == 0 {
		return permissions, nil
	}
	err := c.db.WithContext(ctx).Where("file_record_id IN ?", fileRecordIDs).Find(&permissions).Error
	return permissions, err
}

// FindInternalElectronicPermissionsByFileRecordIDs 根据文件记录ID列表查询内发电子文件权限记录
// 功能: 查询指定文件记录ID列表对应的内发电子文件权限记录（只包含查询权限和查询/下载权限）
// 参数:
//   - ctx: 上下文
//   - fileRecordIDs: 文件记录ID列表
//
// 返回值:
//   - permissions: 内发电子文件权限记录列表
//   - err: 错误信息
func (c *DistributeRecordPermissionClient) FindInternalElectronicPermissionsByFileRecordIDs(ctx context.Context, fileRecordIDs []string) ([]DistributeRecordPermission, error) {
	var permissions []DistributeRecordPermission
	if len(fileRecordIDs) == 0 {
		return permissions, nil
	}

	// 查询条件：
	// 1. file_record_id IN (fileRecordIDs)
	// 2. file_form = 1 (电子文件)
	// 3. file_permission IN (1, 2) (查询权限或查询/下载权限)
	err := c.db.WithContext(ctx).
		Where("file_record_id IN ?", fileRecordIDs).
		Where("file_form = ?", 1).                    // 电子文件
		Where("file_permission IN ?", []int32{1, 2}). // 查询权限(1)或查询/下载权限(2)
		Find(&permissions).Error

	return permissions, err
}

// FindByFileRecordIDsWithStatus 根据文件记录ID列表查询权限记录，同时获取发放记录状态
// 功能: 根据发放记录文件ID列表查询对应的用户权限记录，并关联查询发放记录状态
// 参数:
//   - ctx: 上下文
//   - fileRecordIDs: 文件记录ID列表
//
// 返回值:
//   - permissions: 权限记录列表（包含发放记录状态）
//   - err: 错误信息
func (c *DistributeRecordPermissionClient) FindByFileRecordIDsWithStatus(ctx context.Context, fileRecordIDs []string, userId string) ([]DistributeRecordPermissionWithStatus, error) {
	if len(fileRecordIDs) == 0 {
		return make([]DistributeRecordPermissionWithStatus, 0), nil
	}

	var permissions []DistributeRecordPermissionWithStatus
	query := c.db.WithContext(ctx).Table("distribute_record_permissions").
		Select("distribute_record_permissions.*, distribute_records.status as distribute_record_status").
		Joins("INNER JOIN distribute_record_files ON distribute_record_permissions.file_record_id = distribute_record_files.id").
		Joins("INNER JOIN distribute_records ON distribute_record_files.record_id = distribute_records.id").
		Where("distribute_record_permissions.file_record_id IN ?", fileRecordIDs)
		// 关联查询发放记录状态
	if userId != "" {
		query = query.Where("distribute_record_permissions.user_id = ?", userId)
	}
	err := query.Find(&permissions).Error

	return permissions, err
}

// GetPaperDocumentUsers 获取纸质文件发放审批中，已发放且用户状态为（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中）的用户
// 参数:
//   - ctx: 上下文
//   - fileId: 文件ID
//   - fileFrom: 文件类型，1内部文件 | 2外部文件
//   - filePermission: 文件权限，1查阅 | 2查阅/下载 | 3一次下载
//
// 返回:
//   - []value.DetailedUserPermission: 详细的用户权限信息列表
//   - error: 错误信息，成功时返回nil
func (c *DistributeRecordPermissionClient) GetPaperDocumentUsers(ctx context.Context, fileIds []string) ([]DetailedUserPermission, error) {
	if len(fileIds) == 0 {
		return make([]DetailedUserPermission, 0), nil
	}

	var result []DetailedUserPermission
	err := c.db.WithContext(ctx).
		Table("distribute_record_permissions as p").
		Select("p.user_id, r.status as distribute_status, p.sign_for_status, p.dispose_status").
		Joins("LEFT JOIN distribute_record_files as f ON p.file_record_id = f.id").
		Joins("LEFT JOIN distribute_records as r ON f.record_id = r.id").
		Where("f.file_id in ? AND p.file_form = 2 AND p.file_permission = 3 AND r.status IN (2, 3) AND p.dispose_status in (1, 2, 3, 4)",
			fileIds).
		Group("p.user_id, r.status, p.sign_for_status, p.dispose_status").
		Find(&result).Error

	if err != nil {
		logc.Error(ctx, "Failed to get detailed user permissions", err)
		return nil, err
	}

	return result, nil
}

type DetailedUserPermission struct {
	UserID           string `gorm:"column:user_id"`
	DistributeStatus int    `gorm:"column:distribute_status"`
	SignForStatus    int32  `gorm:"column:sign_for_status"`
	DisposeStatus    int32  `gorm:"column:dispose_status"`
}
