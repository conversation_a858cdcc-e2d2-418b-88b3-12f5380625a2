package mapper

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameInternalDocumentLibrary = "internal_document_library"
)

type InternalDocumentLibraryClient struct {
	db *gorm.DB
}

func NewInternalDocumentLibraryClient(db *DocvaultDB) *InternalDocumentLibraryClient {
	return &InternalDocumentLibraryClient{db: db.GetDB()}
}

func (c *InternalDocumentLibraryClient) GetByID(ctx context.Context, id string) (InternalDocumentLibrary, error) {
	internalDocument := InternalDocumentLibrary{}
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&internalDocument).Error; err != nil {
		return InternalDocumentLibrary{}, err
	}
	return internalDocument, nil
}

type InternalDocumentLibraryPageReq struct {
	Page           int
	PageSize       int
	NoPage         bool
	Ids            []string
	DocCategoryIds []string
	DepartmentIds  []string
	Status         int32
	OrganizationID string
	HasAttachment  int32
	Name           string
	No             string
	OriginalNo     string
}

func (c *InternalDocumentLibraryClient) Page(ctx context.Context, pageReq InternalDocumentLibraryPageReq) (docs []InternalDocumentLibrary, total int64, err error) {
	query := c.db.WithContext(ctx).Table(TableNameInternalDocumentLibrary).Where("organization_id = ?", pageReq.OrganizationID)
	if len(pageReq.Ids) > 0 {
		query = query.Where("id in ?", pageReq.Ids)
	}
	if pageReq.Status != 0 {
		query = query.Where("status = ?", pageReq.Status)
	}
	if len(pageReq.DocCategoryIds) > 0 {
		query = query.Where("doc_category_id in ?", pageReq.DocCategoryIds)
	}
	if len(pageReq.DepartmentIds) > 0 {
		// 0.4版本调整：department_id 现在是多选字段（顿号分隔），需要使用 LIKE 查询
		var departmentConditions []string
		var departmentArgs []interface{}
		for _, deptID := range pageReq.DepartmentIds {
			departmentConditions = append(departmentConditions, "department_id LIKE ?")
			departmentArgs = append(departmentArgs, "%"+deptID+"%")
		}
		if len(departmentConditions) > 0 {
			query = query.Where("("+strings.Join(departmentConditions, " OR ")+")", departmentArgs...)
		}
	}
	if pageReq.Name != "" {
		query = query.Where("name like ?", "%"+pageReq.Name+"%")
	}
	if pageReq.No != "" {
		query = query.Where("no like ?", "%"+pageReq.No+"%")
	}
	if pageReq.OriginalNo != "" {
		query = query.Where("original_no like ?", "%"+pageReq.OriginalNo+"%")
	}

	if pageReq.HasAttachment == 1 {
		query = query.Where("file_id is not null and file_id != ''")
	} else if pageReq.HasAttachment == 2 {
		query = query.Where("file_id is null or file_id = ''")
	}

	query = query.Count(&total).Order("no_prefix, no desc")

	if !pageReq.NoPage {
		query = query.Offset(int((pageReq.Page - 1) * pageReq.PageSize)).Limit(int(pageReq.PageSize))
	}

	if err := query.Find(&docs).Error; err != nil {
		return nil, 0, err
	}
	return docs, total, nil
}

// InternalDocumentLibrary 是 internal_document_library 表的GORM模型
type InternalDocumentLibrary struct {
	ID                    string         `gorm:"type:varchar(64);primary_key"`
	MainID                string         `gorm:"type:varchar(64);column:main_id"`              // 自关联ID 仅在有历史版本时有值
	OrganizationID        string         `gorm:"type:varchar(64);column:organization_id"`      // 子公司ID
	NoPrefix              string         `gorm:"type:varchar(128);column:no_prefix"`           // 编号前缀
	No                    string         `gorm:"type:varchar(128);column:no"`                  // 编号
	SerialNo              int            `gorm:"type:int(11);column:serial_no"`                // 编号序列号
	Name                  string         `gorm:"type:varchar(255);column:name"`                // 文件名称
	EnglishName           string         `gorm:"type:varchar(255);column:english_name"`        // 文件英文名称
	FileID                string         `gorm:"type:varchar(255);column:file_id"`             // 文件id
	DocCategoryID         string         `gorm:"type:varchar(64);column:doc_category_id"`      // 类别ID
	DepartmentIDs         string         `gorm:"type:varchar(500);column:department_ids"`      // 编制部门ID
	AuthorIDs             string         `gorm:"type:varchar(500);column:author_ids"`          // 编制人ID
	Status                int8           `gorm:"type:tinyint;column:status"`                   // 状态
	ApprovalInfo          datatypes.JSON `gorm:"type:json;column:approval_info"`               // 审批信息
	VersionNo             int            `gorm:"type:int(11);column:version_no"`               // 版本号
	Version               string         `gorm:"type:varchar(255);column:version"`             // 版本
	PublishDate           time.Time      `gorm:"type:date;column:publish_date"`                // 发布日期
	EffectiveDate         time.Time      `gorm:"type:date;column:effective_date"`              // 实施日期
	OriginalNo            string         `gorm:"type:varchar(128);column:original_no"`         // 原文件编号
	OriginalVersionNo     string         `gorm:"type:varchar(128);column:original_version_no"` // 原文件版本
	Remark                string         `gorm:"type:varchar(500);column:remark"`              // 备注
	OperationType         int8           `gorm:"type:tinyint;column:operation_type"`           // 操作类型 0-无操作 1- 新增,2- 修订,3-作废 仅在有历史版本时有值
	CreatedAt             time.Time      `gorm:"column:created_at"`                            // 创建时间
	UpdatedAt             time.Time      `gorm:"column:updated_at"`                            // 更新时间
	CreatedBy             string         `gorm:"type:varchar(64);column:created_by"`           // 创建人
	UpdatedBy             string         `gorm:"type:varchar(64);column:updated_by"`           // 更新人
	TenantID              string         `gorm:"type:varchar(64);column:tenant_id"`
	ReplacementDocName    string         `gorm:"type:varchar(255);column:replacement_doc_name"`    // 替代文件名称
	ReplacementDocVersion string         `gorm:"type:varchar(255);column:replacement_doc_version"` // 替代版本版次
}

// TableName 指定GORM模型对应的表名
func (InternalDocumentLibrary) TableName() string {
	return TableNameInternalDocumentLibrary
}

type ApprovalInfo struct {
	Auditors  []ApprovalItem `json:"auditors"`  // 审核人
	Approvers []ApprovalItem `json:"approvers"` // 批准人
}

type ApprovalItem struct {
	UserID     string `json:"userId"`
	PassedDate int64  `json:"passedDate"`
}

func (i InternalDocumentLibrary) GetApprovalInfo() ApprovalInfo {
	approvalInfo, err := i.ApprovalInfo.MarshalJSON()
	if err != nil {
		return ApprovalInfo{}
	}
	approvalInfoValue := ApprovalInfo{}
	if err := json.Unmarshal(approvalInfo, &approvalInfoValue); err != nil {
		return ApprovalInfo{}
	}
	return approvalInfoValue
}

func (i *InternalDocumentLibrary) SetApprovalInfo(approvalInfo ApprovalInfo) {
	approvalInfoBytes, err := json.Marshal(approvalInfo)
	if err != nil {
		return
	}
	i.ApprovalInfo = datatypes.JSON(approvalInfoBytes)
}

// GetDepartmentIDs 获取编制部门ID列表
// 功能：将顿号分隔的编制部门ID字符串转换为切片
// 返回值：编制部门ID切片
func (i InternalDocumentLibrary) GetDepartmentIDs() []string {
	if i.DepartmentIDs == "" {
		return []string{}
	}
	return strings.Split(i.DepartmentIDs, "、")
}

// BatchGetDocumentNames 批量根据文档ID和版本号查询文档名称
// 功能: 根据文档ID和版本号列表批量查询文档名称
// 参数:
//   - ctx: 上下文
//   - docIDVersions: 文档ID和版本号的映射
//
// 返回值:
//   - map[string]string: 文档ID+版本号为key，文档名称为value的映射
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) BatchGetDocumentNames(ctx context.Context, docIDVersions map[string]string) (map[string]string, error) {
	if len(docIDVersions) == 0 {
		return make(map[string]string), nil
	}

	// 构建查询条件
	var conditions []string
	var args []interface{}
	for docID, versionNo := range docIDVersions {
		conditions = append(conditions, "(id = ? AND version_no = ?)")
		args = append(args, docID, versionNo)
	}

	var docs []struct {
		ID        string `gorm:"column:id"`
		VersionNo int    `gorm:"column:version_no"`
		Name      string `gorm:"column:name"`
	}

	query := "(" + strings.Join(conditions, " OR ") + ")"
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Select("id, version_no, name").
		Where(query, args...).
		Find(&docs).Error; err != nil {
		return nil, err
	}

	// 构建结果映射
	result := make(map[string]string)
	for _, doc := range docs {
		key := fmt.Sprintf("%s_%d", doc.ID, doc.VersionNo)
		result[key] = doc.Name
	}

	return result, nil
}

// GetByIDs 根据ID列表批量查询内部文档
// 功能: 根据文档ID列表批量查询内部文档信息
// 参数:
//   - ctx: 上下文
//   - ids: 文档ID列表
//
// 返回值:
//   - []InternalDocumentLibrary: 内部文档列表
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) GetByIDs(ctx context.Context, ids []string) ([]InternalDocumentLibrary, error) {
	var docs []InternalDocumentLibrary
	if err := c.db.WithContext(ctx).Table(TableNameInternalDocumentLibrary).Where("id IN ?", ids).Find(&docs).Error; err != nil {
		return nil, err
	}
	return docs, nil
}

// GetDocumentIDsByName 根据文档名称模糊查询文档ID列表
// 功能: 根据文档名称进行模糊查询，获取匹配的文档ID列表
// 参数:
//   - ctx: 上下文
//   - name: 文档名称（支持模糊匹配）
//
// 返回值:
//   - []string: 匹配的文档ID列表
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) GetDocumentIDsByName(ctx context.Context, name string) ([]string, error) {
	if name == "" {
		return []string{}, nil
	}

	var documentIDs []string
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Select("id").
		Where("name LIKE ?", "%"+name+"%").
		Find(&documentIDs).Error; err != nil {
		return nil, err
	}

	return documentIDs, nil
}

// GetDocumentIDsByNo 根据文档编号模糊查询文档ID列表
// 功能: 根据文档编号进行模糊查询，获取匹配的文档ID列表
// 参数:
//   - ctx: 上下文
//   - no: 文档编号（支持模糊匹配）
//
// 返回值:
//   - []string: 匹配的文档ID列表
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) GetDocumentIDsByNo(ctx context.Context, no string) ([]string, error) {
	if no == "" {
		return []string{}, nil
	}

	var documentIDs []string
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Select("id").
		Where("no LIKE ?", "%"+no+"%").
		Find(&documentIDs).Error; err != nil {
		return nil, err
	}

	return documentIDs, nil
}

// GetChangeRecords 查询内部文档变更记录
// 功能：根据文档ID查询该文档的所有历史版本记录，按更新时间倒序排列
// 参数：ctx - 上下文，documentID - 文档ID，page - 页码，pageSize - 每页大小，noPage - 是否不分页
// 返回值：变更记录列表，总数，错误信息
func (c *InternalDocumentLibraryClient) GetChangeRecords(ctx context.Context, documentID string, page, pageSize int, noPage bool) ([]InternalDocumentLibrary, int64, error) {
	var records []InternalDocumentLibrary
	var total int64

	// 构建查询条件：查询 main_id = documentID 的所有历史版本记录
	query := c.db.WithContext(ctx).Where("main_id = ?", documentID)

	// 获取总数
	if err := query.Model(&InternalDocumentLibrary{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 按更新时间倒序排列
	query = query.Order("updated_at DESC")

	// 根据 noPage 参数决定是否分页
	if !noPage {
		// 分页查询
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
