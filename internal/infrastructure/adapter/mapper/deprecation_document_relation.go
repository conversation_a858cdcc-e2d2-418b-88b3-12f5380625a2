package mapper

import (
	"context"
	
	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameDeprecationDocumentRelation = "deprecation_document_relations"
)

// DeprecationDocumentRelation 对应 deprecation_document_relations 表
type DeprecationDocumentRelation struct {
	ID                  string `gorm:"type:varchar(64);primary_key"`
	DeprecationRecordID string `gorm:"type:varchar(64);index;comment:'作废记录ID'"`
	DocumentID          string `gorm:"type:varchar(64);index;comment:'文档ID'"`
}

func (DeprecationDocumentRelation) TableName() string {
	return TableNameDeprecationDocumentRelation
}

// DeprecationDocumentRelationClient 是 deprecation_document_relations 表的数据访问客户端
type DeprecationDocumentRelationClient struct {
	db *gorm.DB
}

// NewDeprecationDocumentRelationClient 创建一个新的 DeprecationDocumentRelationClient 实例
// 功能: 创建并初始化作废文档关系数据访问客户端
// 参数:
//
//	db: DocvaultDB数据库连接实例
//
// 返回值:
//
//	*DeprecationDocumentRelationClient: 作废文档关系客户端实例
//
// 异常: 无
func NewDeprecationDocumentRelationClient(db *DocvaultDB) *DeprecationDocumentRelationClient {
	return &DeprecationDocumentRelationClient{
		db: db.GetDB(),
	}
}

// GetByDeprecationRecordID 根据作废记录ID获取所有关联的文档关系
// 功能: 查询指定作废记录的所有文档关系，按ID排序保证顺序
// 参数:
//
//	ctx: 上下文对象
//	deprecationRecordID: 作废记录ID
//
// 返回值:
//
//	[]DeprecationDocumentRelation: 作废文档关系列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationDocumentRelationClient) GetByDeprecationRecordID(ctx context.Context, deprecationRecordID string) ([]DeprecationDocumentRelation, error) {
	var relations []DeprecationDocumentRelation
	if err := c.db.WithContext(ctx).Where("deprecation_record_id = ?", deprecationRecordID).Order("id ASC").Find(&relations).Error; err != nil {
		logc.Error(ctx, "Failed to get deprecation document relations by deprecation record ID", err)
		return nil, err
	}
	return relations, nil
}

// Create 创建作废文档关系记录
// 功能：创建一条新的作废文档关系记录
// 参数：ctx - 上下文，relation - 作废文档关系对象
// 返回值：错误信息
func (c *DeprecationDocumentRelationClient) Create(ctx context.Context, relation *DeprecationDocumentRelation) error {
	if err := c.db.WithContext(ctx).Create(relation).Error; err != nil {
		logc.Error(ctx, "Failed to create deprecation document relation", err)
		return err
	}
	return nil
}

// BatchCreate 批量创建作废文档关系记录
// 功能：批量创建作废文档关系记录
// 参数：ctx - 上下文，relations - 作废文档关系对象列表
// 返回值：错误信息
func (c *DeprecationDocumentRelationClient) BatchCreate(ctx context.Context, relations []DeprecationDocumentRelation) error {
	if len(relations) == 0 {
		return nil
	}
	if err := c.db.WithContext(ctx).Create(&relations).Error; err != nil {
		logc.Error(ctx, "Failed to batch create deprecation document relations", err)
		return err
	}
	return nil
}

// DeleteByDeprecationRecordID 根据作废记录ID删除所有关联的文档关系
// 功能：删除指定作废记录的所有文档关系
// 参数：ctx - 上下文，deprecationRecordID - 作废记录ID
// 返回值：错误信息
func (c *DeprecationDocumentRelationClient) DeleteByDeprecationRecordID(ctx context.Context, deprecationRecordID string) error {
	if err := c.db.WithContext(ctx).Where("deprecation_record_id = ?", deprecationRecordID).Delete(&DeprecationDocumentRelation{}).Error; err != nil {
		logc.Error(ctx, "Failed to delete deprecation document relations by deprecation record ID", err)
		return err
	}
	return nil
}

// BatchGetDocumentCounts 批量获取作废记录的文档数量统计
// 功能: 批量查询作废记录对应的文档数量
// 参数:
//
//	ctx: 上下文对象
//	deprecationRecordIDs: 作废记录ID列表
//
// 返回值:
//
//	map[string]int32: 作废记录ID到文档数量的映射
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationDocumentRelationClient) BatchGetDocumentCounts(ctx context.Context, deprecationRecordIDs []string) (map[string]int32, error) {
	if len(deprecationRecordIDs) == 0 {
		return make(map[string]int32), nil
	}

	type CountResult struct {
		DeprecationRecordID string `gorm:"column:deprecation_record_id"`
		DocumentCount       int32  `gorm:"column:document_count"`
	}

	var results []CountResult
	if err := c.db.WithContext(ctx).
		Table(TableNameDeprecationDocumentRelation).
		Select("deprecation_record_id, COUNT(*) as document_count").
		Where("deprecation_record_id IN ?", deprecationRecordIDs).
		Group("deprecation_record_id").
		Find(&results).Error; err != nil {
		logc.Error(ctx, "Failed to batch get document counts", err)
		return nil, err
	}

	countMap := make(map[string]int32)
	for _, result := range results {
		countMap[result.DeprecationRecordID] = result.DocumentCount
	}

	// 确保所有请求的记录ID都有对应的数量（没有文档的记录数量为0）
	for _, recordID := range deprecationRecordIDs {
		if _, exists := countMap[recordID]; !exists {
			countMap[recordID] = 0
		}
	}

	return countMap, nil
}
