package shared

import (
	"context"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/utils"
)

const (
	// 角色代码常量
	GroupFileManagerRoleCode      = "JTWJGLY"  // 集团文件管理员角色代码
	SubsidiaryFileManagerRoleCode = "ZGSWJGLY" // 子公司文件管理员角色代码
)

// OrganizationAuth 组织权限查询器
// 功能：根据用户角色和组织层级，获取用户可查看的组织ID列表
type OrganizationAuth struct {
	svcCtx *svc.ServiceContext
}

// NewOrganizationAuth 创建组织权限查询器实例
// 功能：初始化组织权限查询器
// 参数：
//   - svcCtx: 服务上下文
//
// 返回值：
//   - *OrganizationAuth: 组织权限查询器实例
func NewOrganizationAuth(svcCtx *svc.ServiceContext) *OrganizationAuth {
	return &OrganizationAuth{svcCtx: svcCtx}
}

// GetUserViewableOrganizationIDs 获取用户可查看的组织ID列表
// 功能：根据用户角色和组织层级，返回用户有权限查看的组织ID列表
// 实现逻辑：
//  1. 获取当前登录用户信息
//  2. 获取用户所属组织信息
//  3. 根据组织类型（集团/子公司）和用户角色权限确定可查看的组织范围
//
// 参数：
//   - ctx: 上下文
//
// 返回值：
//   - []string: 用户可查看的组织ID列表
//   - error: 错误信息
func (o *OrganizationAuth) GetUserViewableOrganizationIDs(ctx context.Context) ([]string, error) {
	// 1. 获取当前登录用户信息
	userLoginInfo := utils.GetCurrentLoginUser(ctx)

	// 2. 获取用户所属组织信息
	organization, err := o.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, userLoginInfo.OrganizationId)
	if err != nil {
		return nil, err
	}

	// 3. 根据组织类型确定权限范围
	if o.isGroupOrganization(organization.ParentId) {
		// 处理集团组织权限
		return o.handleGroupOrganizationAuth(ctx, userLoginInfo.UserId, organization.Id)
	} else {
		// 处理子公司组织权限
		return o.handleSubsidiaryOrganizationAuth(ctx, userLoginInfo.UserId, organization.Id)
	}
}

// isGroupOrganization 判断是否为集团组织
// 功能：通过父组织ID判断当前组织是否为集团（顶级组织）
// 参数：
//   - parentId: 父组织ID
//
// 返回值：
//   - bool: true表示集团组织，false表示子公司
func (o *OrganizationAuth) isGroupOrganization(parentId string) bool {
	return parentId == ""
}

// handleGroupOrganizationAuth 处理集团组织权限
// 功能：根据用户是否为集团文件管理员，返回相应的组织ID列表
// 参数：
//   - ctx: 上下文
//   - userId: 用户ID
//   - organizationId: 当前组织ID
//
// 返回值：
//   - []string: 可查看的组织ID列表
//   - error: 错误信息
func (o *OrganizationAuth) handleGroupOrganizationAuth(ctx context.Context, userId, organizationId string) ([]string, error) {
	// 检查是否为集团文件管理员
	isGroupFileManager, err := o.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userId, GroupFileManagerRoleCode)
	if err != nil {
		return nil, err
	}

	if isGroupFileManager {
		// 集团文件管理员：返回所有下属组织ID（包括集团本身）
		return o.getOrganizationIDsWithDescendants(ctx, organizationId)
	}

	// 普通集团用户：只能查看本集团
	return []string{organizationId}, nil
}

// handleSubsidiaryOrganizationAuth 处理子公司组织权限
// 功能：根据用户是否为子公司文件管理员，返回相应的组织ID列表
// 参数：
//   - ctx: 上下文
//   - userId: 用户ID
//   - organizationId: 当前组织ID
//
// 返回值：
//   - []string: 可查看的组织ID列表
//   - error: 错误信息
func (o *OrganizationAuth) handleSubsidiaryOrganizationAuth(ctx context.Context, userId, organizationId string) ([]string, error) {
	// 检查是否为子公司文件管理员
	isSubsidiaryFileManager, err := o.svcCtx.PhoenixClient.CheckUserHasRoleCode(ctx, userId, SubsidiaryFileManagerRoleCode)
	if err != nil {
		return nil, err
	}

	if isSubsidiaryFileManager {
		// 子公司文件管理员：返回所有同级子公司ID
		return o.getOrganizationIDsWithDescendants(ctx, organizationId)
	}

	// 普通子公司用户：只能查看本子公司
	return []string{organizationId}, nil
}

// getOrganizationIDsWithDescendants 获取组织ID及其所有后代组织ID
// 功能：获取指定组织及其所有后代组织的ID列表（包括自身）
// 参数：
//   - ctx: 上下文
//   - organizationId: 组织ID
//
// 返回值：
//   - []string: 组织ID及其所有后代组织ID列表
//   - error: 错误信息
func (o *OrganizationAuth) getOrganizationIDsWithDescendants(ctx context.Context, organizationId string) ([]string, error) {
	descendants, err := mapper.NewOrganizationClosureClient(o.svcCtx.PhoenixDB).GetDescendantsByAncestorID(ctx, organizationId)
	if err != nil {
		return nil, err
	}

	// 提取所有后代组织ID
	organizationIDs := make([]string, 0, len(descendants))
	for _, descendant := range descendants {
		organizationIDs = append(organizationIDs, descendant.DescendantID)
	}
	// 包含自身
	organizationIDs = append(organizationIDs, organizationId)
	return organizationIDs, nil
}
