package assemblers

// QueryResult 查询结果结构
// 功能：存储并发查询的结果
type QueryResult struct {
	UserNicknames   map[string]string // 用户昵称映射
	DocumentCounts  map[string]int32  // 文档数量统计映射
}

// InternalDocumentQueryResult 内部文档查询结果结构
// 功能：存储内部文档并发查询的结果
type InternalDocumentQueryResult struct {
	CategoryNames map[string]string // 文档类别名称映射
}

// ExternalDocumentQueryResult 外部文档查询结果结构
// 功能：存储外部文档并发查询的结果
type ExternalDocumentQueryResult struct {
	TypeNames map[string]string // 文档类型名称映射
}