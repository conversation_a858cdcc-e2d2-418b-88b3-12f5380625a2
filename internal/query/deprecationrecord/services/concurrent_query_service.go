package services

import (
	"context"
	"encoding/json"
	"sync"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
)

// CollectedData 收集的数据结构
// 功能：存储从作废记录中收集的需要查询的数据
type CollectedData struct {
	UserIDs              []string // 用户ID列表
	DeprecationRecordIDs []string // 作废记录ID列表
}

// QueryResult 查询结果结构
// 功能：存储并发查询的结果
type QueryResult struct {
	UserNicknames   map[string]string                           // 用户昵称映射
	DocumentCounts  map[string]int32                           // 文档数量统计映射
	DocumentDetails map[string][]mapper.DeprecationDocumentInfo // 文档详情映射
}

// InternalDocumentQueryResult 内部文档查询结果结构
// 功能：存储内部文档并发查询的结果
type InternalDocumentQueryResult struct {
	CategoryNames map[string]string // 文档类别名称映射
}

// ExternalDocumentQueryResult 外部文档查询结果结构
// 功能：存储外部文档并发查询的结果
type ExternalDocumentQueryResult struct {
	TypeNames map[string]string // 文档类型名称映射
}

// ConcurrentQueryService 并发查询服务
// 功能：处理用户昵称、文档统计和文档详情的并发查询
type ConcurrentQueryService struct {
	svcCtx *svc.ServiceContext
}

// NewConcurrentQueryService 创建并发查询服务
// 功能：创建并发查询服务实例
// 参数：svcCtx - 服务上下文
// 返回值：并发查询服务实例
func NewConcurrentQueryService(svcCtx *svc.ServiceContext) *ConcurrentQueryService {
	return &ConcurrentQueryService{
		svcCtx: svcCtx,
	}
}

// QueryConcurrently 并发查询用户昵称和文档相关信息
// 功能：使用协程并发查询用户昵称、文档数量统计和文档详情
// 参数：ctx - 上下文，deprecationRecords - 作废记录列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryConcurrently(ctx context.Context, deprecationRecords []mapper.DeprecationRecord) (*QueryResult, error) {
	// 实现步骤：
	// 1. 从作废记录中收集需要查询的数据
	// 2. 启动三个协程分别查询用户昵称、文档数量统计和文档详情
	// 3. 等待所有协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromRecords(deprecationRecords)

	// 2. 并发查询
	var wg sync.WaitGroup
	var userNicknames map[string]string
	var documentCounts map[string]int32
	var documentDetails map[string][]mapper.DeprecationDocumentInfo
	var userErr, documentCountErr, documentDetailsErr error

	// 协程1: 查询用户昵称
	if len(collectedData.UserIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			userClient := mapper.NewUserClient(s.svcCtx.PhoenixDB)
			userNicknames, userErr = userClient.BatchGetUserNicknames(ctx, collectedData.UserIDs)
		}()
	} else {
		userNicknames = make(map[string]string)
	}

	// 协程2: 查询文档数量统计
	if len(collectedData.DeprecationRecordIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			deprecationRelationClient := mapper.NewDeprecationDocumentRelationClient(s.svcCtx.DocvaultDB)
			documentCounts, documentCountErr = deprecationRelationClient.BatchGetDocumentCounts(ctx, collectedData.DeprecationRecordIDs)
		}()
	} else {
		documentCounts = make(map[string]int32)
	}

	// 协程3: 查询文档详情
	if len(collectedData.DeprecationRecordIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			deprecationRecordClient := mapper.NewDeprecationRecordClient(s.svcCtx.DocvaultDB)
			documentDetails, documentDetailsErr = s.batchGetDocumentDetails(ctx, deprecationRecordClient, collectedData.DeprecationRecordIDs)
		}()
	} else {
		documentDetails = make(map[string][]mapper.DeprecationDocumentInfo)
	}

	// 3. 等待所有协程完成
	wg.Wait()

	// 4. 检查错误
	if userErr != nil {
		return nil, userErr
	}
	if documentCountErr != nil {
		return nil, documentCountErr
	}
	if documentDetailsErr != nil {
		return nil, documentDetailsErr
	}

	return &QueryResult{
		UserNicknames:   userNicknames,
		DocumentCounts:  documentCounts,
		DocumentDetails: documentDetails,
	}, nil
}

// collectDataFromRecords 从作废记录中收集需要查询的数据
// 功能：提取作废记录中的用户ID和作废记录ID
// 参数：deprecationRecords - 作废记录列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromRecords(deprecationRecords []mapper.DeprecationRecord) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历作废记录，收集用户ID和作废记录ID
	// 3. 去重用户ID
	// 4. 返回收集到的数据

	userIDs := make([]string, 0)
	deprecationRecordIDs := make([]string, 0, len(deprecationRecords))
	userIDSet := make(map[string]bool)

	for _, record := range deprecationRecords {
		// 收集作废记录ID
		deprecationRecordIDs = append(deprecationRecordIDs, record.ID)

		// 收集创建用户ID（去重）
		if !userIDSet[record.CreatedBy] {
			userIDs = append(userIDs, record.CreatedBy)
			userIDSet[record.CreatedBy] = true
		}

		// 收集更新用户ID（去重）
		if record.UpdatedBy != "" && !userIDSet[record.UpdatedBy] {
			userIDs = append(userIDs, record.UpdatedBy)
			userIDSet[record.UpdatedBy] = true
		}

		// 收集审批信息中的用户ID
		s.collectApprovalUserIDs(record.ApprovalInfo, &userIDs, userIDSet)
	}

	return &CollectedData{
		UserIDs:              userIDs,
		DeprecationRecordIDs: deprecationRecordIDs,
	}
}

// collectApprovalUserIDs 收集审批信息中的用户ID
// 功能：解析approval_info字段，提取其中的用户ID
// 参数：approvalInfo - JSON字符串格式的审批信息，userIDs - 用户ID列表指针，userIDSet - 用户ID集合（用于去重）
func (s *ConcurrentQueryService) collectApprovalUserIDs(approvalInfo []byte, userIDs *[]string, userIDSet map[string]bool) {
	if len(approvalInfo) == 0 {
		return
	}

	approvalInfoStr := string(approvalInfo)
	if approvalInfoStr == "" || approvalInfoStr == "{}" {
		return
	}

	type ApprovalData struct {
		Auditors []struct {
			UserID string `json:"user_id"`
		} `json:"auditors"`
		Approvers []struct {
			UserID string `json:"user_id"`
		} `json:"approvers"`
	}

	var data ApprovalData
	if err := json.Unmarshal(approvalInfo, &data); err != nil {
		return
	}

	// 收集审核人用户ID
	for _, auditor := range data.Auditors {
		if auditor.UserID != "" && !userIDSet[auditor.UserID] {
			*userIDs = append(*userIDs, auditor.UserID)
			userIDSet[auditor.UserID] = true
		}
	}

	// 收集批准人用户ID
	for _, approver := range data.Approvers {
		if approver.UserID != "" && !userIDSet[approver.UserID] {
			*userIDs = append(*userIDs, approver.UserID)
			userIDSet[approver.UserID] = true
		}
	}
}

// batchGetDocumentDetails 批量获取文档详情
// 功能：批量查询作废记录的文档详情
// 参数：ctx - 上下文，client - 作废记录客户端，deprecationRecordIDs - 作废记录ID列表
// 返回值：作废记录ID到文档详情列表的映射，错误信息
func (s *ConcurrentQueryService) batchGetDocumentDetails(ctx context.Context, client *mapper.DeprecationRecordClient, deprecationRecordIDs []string) (map[string][]mapper.DeprecationDocumentInfo, error) {
	results := make(map[string][]mapper.DeprecationDocumentInfo)

	for _, recordID := range deprecationRecordIDs {
		documents, err := client.GetDocumentsByDeprecationRecordID(ctx, recordID)
		if err != nil {
			return nil, err
		}
		results[recordID] = documents
	}

	return results, nil
}

// QueryUserNicknames 查询用户昵称
// 功能：批量查询用户昵称
// 参数：ctx - 上下文，userIDs - 用户ID列表
// 返回值：用户ID到昵称的映射，错误信息
func (s *ConcurrentQueryService) QueryUserNicknames(ctx context.Context, userIDs []string) (map[string]string, error) {
	if len(userIDs) == 0 {
		return make(map[string]string), nil
	}

	userClient := mapper.NewUserClient(s.svcCtx.PhoenixDB)
	return userClient.BatchGetUserNicknames(ctx, userIDs)
}

// QueryDocumentCounts 查询文档数量统计
// 功能：批量查询作废记录的文档数量统计
// 参数：ctx - 上下文，deprecationRecordIDs - 作废记录ID列表
// 返回值：作废记录ID到文档数量统计的映射，错误信息
func (s *ConcurrentQueryService) QueryDocumentCounts(ctx context.Context, deprecationRecordIDs []string) (map[string]int32, error) {
	if len(deprecationRecordIDs) == 0 {
		return make(map[string]int32), nil
	}

	deprecationRelationClient := mapper.NewDeprecationDocumentRelationClient(s.svcCtx.DocvaultDB)
	return deprecationRelationClient.BatchGetDocumentCounts(ctx, deprecationRecordIDs)
}

// ==================== 内部文档相关方法 ====================

// QueryInternalDocumentsConcurrently 并发查询内部文档类别名称等信息
// 功能：使用协程并发查询内部文档类别名称
// 参数：ctx - 上下文，deprecatedDocuments - 内部文档库列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryInternalDocumentsConcurrently(ctx context.Context, deprecatedDocuments []mapper.InternalDocumentLibrary) (*InternalDocumentQueryResult, error) {
	// 实现步骤：
	// 1. 从内部文档中收集需要查询的数据
	// 2. 启动协程查询文档类别名称
	// 3. 等待协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	_ = s.collectDataFromInternalDocuments(deprecatedDocuments) // 暂时不使用，但保留接口

	// 2. 并发查询
	var wg sync.WaitGroup
	var categoryNames map[string]string

	// 协程: 查询文档类别名称
	// TODO: 实现文档类别名称查询
	// 暂时返回空的类别名称映射
	categoryNames = make(map[string]string)

	// 3. 等待所有协程完成
	wg.Wait()

	return &InternalDocumentQueryResult{
		CategoryNames: categoryNames,
	}, nil
}

// collectDataFromInternalDocuments 从内部文档中收集需要查询的数据
// 功能：提取内部文档中的文档类别ID
// 参数：deprecatedDocuments - 内部文档库列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromInternalDocuments(deprecatedDocuments []mapper.InternalDocumentLibrary) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历内部文档，收集文档类别ID
	// 3. 去重文档类别ID
	// 4. 返回收集到的数据

	categoryIDs := make([]string, 0)
	categoryIDSet := make(map[string]bool)

	for _, document := range deprecatedDocuments {
		// 收集文档类别ID（去重）
		if document.DocCategoryID != "" && !categoryIDSet[document.DocCategoryID] {
			categoryIDs = append(categoryIDs, document.DocCategoryID)
			categoryIDSet[document.DocCategoryID] = true
		}
	}

	return &CollectedData{
		UserIDs:              []string{}, // 内部文档不需要用户ID
		DeprecationRecordIDs: []string{}, // 内部文档不需要作废记录ID
	}
}

// ==================== 外部文档相关方法 ====================

// QueryExternalDocumentsConcurrently 并发查询外部文档类型名称等信息
// 功能：使用协程并发查询外部文档类型名称
// 参数：ctx - 上下文，deprecatedDocuments - 外部文档库列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryExternalDocumentsConcurrently(ctx context.Context, deprecatedDocuments []mapper.ExternalDocumentLibrary) (*ExternalDocumentQueryResult, error) {
	// 实现步骤：
	// 1. 从外部文档中收集需要查询的数据
	// 2. 启动协程查询文档类型名称
	// 3. 等待协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	_ = s.collectDataFromExternalDocuments(deprecatedDocuments) // 暂时不使用，但保留接口

	// 2. 并发查询
	var wg sync.WaitGroup
	var typeNames map[string]string

	// 协程: 查询文档类型名称
	// TODO: 实现文档类型名称查询
	// 暂时返回空的类型名称映射
	typeNames = make(map[string]string)

	// 3. 等待所有协程完成
	wg.Wait()

	return &ExternalDocumentQueryResult{
		TypeNames: typeNames,
	}, nil
}

// collectDataFromExternalDocuments 从外部文档中收集需要查询的数据
// 功能：提取外部文档中的文档类型ID
// 参数：deprecatedDocuments - 外部文档库列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromExternalDocuments(deprecatedDocuments []mapper.ExternalDocumentLibrary) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历外部文档，收集文档类型ID
	// 3. 去重文档类型ID
	// 4. 返回收集到的数据

	typeIDs := make([]string, 0)
	typeIDSet := make(map[string]bool)

	for _, document := range deprecatedDocuments {
		// 收集文档类型ID（去重）
		if document.TypeDictionaryNodeId != "" && !typeIDSet[document.TypeDictionaryNodeId] {
			typeIDs = append(typeIDs, document.TypeDictionaryNodeId)
			typeIDSet[document.TypeDictionaryNodeId] = true
		}
	}

	return &CollectedData{
		UserIDs:              []string{}, // 外部文档不需要用户ID
		DeprecationRecordIDs: []string{}, // 外部文档不需要作废记录ID
	}
}